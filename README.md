# Daggerheart Master Bot

A comprehensive Discord bot and web application for managing Daggerheart RPG sessions. This system provides both Discord bot functionality and a modern web interface for campaign management, character tracking, and source material access.

## Features

### Discord Bot
- **Dice Rolling**: Advanced dice rolling with Daggerheart-specific mechanics
- **Character Management**: Create and manage characters with full stat tracking
- **Campaign Tools**: Campaign creation, player management, and session tracking
- **Source Material**: Search spells, equipment, rules, and character options
- **GM Tools**: NPC generation, encounter building, and session management

### Web Interface
- **Dashboard**: Overview of campaigns, characters, and recent activity
- **Campaign Management**: Web-based campaign and session management
- **Character Sheets**: Digital character sheets with real-time updates
- **Source Material Browser**: Searchable database of game content
- **API Access**: RESTful API for integration with other tools

## Technology Stack

- **Backend**: Django 4.2 with Django REST Framework
- **Database**: PostgreSQL
- **Discord Bot**: discord.py 2.3
- **Frontend**: Bootstrap 5 with vanilla JavaScript
- **Caching**: Redis (for Celery background tasks)
- **Deployment**: Docker with docker-compose

## Quick Start

### Prerequisites
- Python 3.11+
- PostgreSQL
- Redis
- Discord Bot Token

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd DaggerfallBot
   ```

2. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up the database**
   ```bash
   python manage.py migrate
   python manage.py createsuperuser
   ```

5. **Run the development server**
   ```bash
   python manage.py runserver
   ```

6. **Run the Discord bot**
   ```bash
   python bot/bot.py
   ```

### Docker Setup

1. **Start services with docker-compose**
   ```bash
   docker-compose up -d
   ```

2. **Run migrations**
   ```bash
   docker-compose exec web python manage.py migrate
   docker-compose exec web python manage.py createsuperuser
   ```

## Configuration

### Environment Variables

Create a `.env` file with the following variables:

```env
# Discord Bot Configuration
DISCORD_TOKEN=your_discord_bot_token_here
DISCORD_GUILD_ID=your_guild_id_here

# Django Configuration
SECRET_KEY=your_secret_key_here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Database Configuration
DATABASE_URL=postgresql://daggerfall_user:password@localhost:5432/daggerfall_db

# Redis Configuration
REDIS_URL=redis://localhost:6379/0
```

### Discord Bot Setup

1. Create a Discord application at https://discord.com/developers/applications
2. Create a bot user and copy the token
3. Invite the bot to your server with appropriate permissions:
   - Send Messages
   - Use Slash Commands
   - Read Message History
   - Embed Links
   - Attach Files

## Usage

### Discord Commands

The bot uses the prefix `!dh` for all commands:

#### Dice Rolling
- `!dh roll 2d12+3` - Roll dice with modifiers
- `!dh action` - Roll action dice (2d12)
- `!dh damage d8+2` - Roll damage dice

#### Character Management
- `!dh character create` - Create a new character
- `!dh character show` - Display character sheet
- `!dh character list` - List all your characters

#### Campaign Management
- `!dh campaign create <name>` - Create a new campaign (GM only)
- `!dh campaign invite @player` - Invite player to campaign
- `!dh campaign info` - Show campaign details

#### Source Material
- `!dh lookup <term>` - Search source material
- `!dh spell <name>` - Look up a spell
- `!dh class <name>` - Look up a character class

#### GM Tools
- `!dh gm npc` - Generate random NPC
- `!dh gm encounter` - Generate encounter
- `!dh gm session start` - Start a game session

### Web Interface

Access the web interface at `http://localhost:8000` to:

- View campaign dashboards
- Manage character sheets
- Browse source material
- Access the admin panel at `/admin/`
- Use the API at `/api/`

## API Documentation

The REST API provides endpoints for:

- `/api/campaigns/` - Campaign management
- `/api/characters/` - Character data
- `/api/dice-rolls/` - Dice roll history
- `/api/spells/` - Spell database
- `/api/equipment/` - Equipment database
- `/api/rules/` - Rules and mechanics

API documentation is available at `/api/` when running the development server.

## Development

### Project Structure

```
DaggerfallBot/
├── bot/                    # Discord bot code
│   ├── cogs/              # Bot command modules
│   └── bot.py             # Main bot file
├── daggerfall_web/       # Django project settings
├── core/                  # Core models (users, guilds, dice)
├── campaigns/             # Campaign management
├── characters/            # Character management
├── source_material/       # Source material database
├── api/                   # REST API endpoints
├── templates/             # HTML templates
├── static/                # CSS, JS, images
└── requirements.txt       # Python dependencies
```

### Adding New Features

1. **Discord Commands**: Add new cogs in `bot/cogs/`
2. **Web Features**: Create new Django apps or extend existing ones
3. **API Endpoints**: Add new viewsets in `api/views.py`
4. **Database Models**: Add models and run migrations

### Testing

Run tests with:
```bash
python manage.py test
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue on GitHub
- Join our Discord server
- Check the documentation

## Acknowledgments

- Built for the Daggerheart RPG system
- Uses the Discord.py library
- Powered by Django and PostgreSQL
