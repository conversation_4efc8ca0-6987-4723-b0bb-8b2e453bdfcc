# Generated by Django 5.2.3 on 2025-06-15 00:26

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='DiscordUser',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('discord_id', models.CharField(max_length=20, unique=True)),
                ('discord_username', models.CharField(max_length=100)),
                ('discord_discriminator', models.CharField(blank=True, max_length=4)),
                ('avatar_url', models.URLField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='Guild',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('guild_id', models.CharField(max_length=20, unique=True)),
                ('name', models.CharField(max_length=100)),
                ('icon_url', models.URLField(blank=True)),
                ('bot_enabled', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='owned_guilds', to='core.discorduser')),
            ],
        ),
        migrations.CreateModel(
            name='DiceRoll',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('roll_type', models.CharField(choices=[('action', 'Action Roll'), ('damage', 'Damage Roll'), ('fear', 'Fear Roll'), ('hope', 'Hope Roll'), ('custom', 'Custom Roll')], max_length=20)),
                ('dice_expression', models.CharField(max_length=100)),
                ('result', models.IntegerField()),
                ('individual_rolls', models.JSONField()),
                ('modifier', models.IntegerField(default=0)),
                ('description', models.TextField(blank=True)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.discorduser')),
                ('guild', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='core.guild')),
            ],
            options={
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='GuildMembership',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_admin', models.BooleanField(default=False)),
                ('joined_at', models.DateTimeField(auto_now_add=True)),
                ('guild', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.guild')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.discorduser')),
            ],
            options={
                'unique_together': {('guild', 'user')},
            },
        ),
        migrations.AddField(
            model_name='guild',
            name='members',
            field=models.ManyToManyField(through='core.GuildMembership', to='core.discorduser'),
        ),
    ]
