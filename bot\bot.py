import os
import sys
import asyncio
import logging
import discord
from discord.ext import commands
from dotenv import load_dotenv
import django
from django.conf import settings

# Add the project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'daggerfall_web.settings')
django.setup()

from core.models import DiscordUser, Guild, DiceRoll
from campaigns.models import Campaign
from characters.models import Character

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger('daggerfall_bot')

# Bot configuration
intents = discord.Intents.default()
intents.message_content = True
intents.guilds = True
intents.members = True

class DaggerfallBot(commands.Bot):
    def __init__(self):
        super().__init__(
            command_prefix='!dh ',
            intents=intents,
            description='<PERSON><PERSON>heart Master Bot - Your companion for Daggerheart RPG sessions'
        )
        
    async def setup_hook(self):
        """Called when the bot is starting up"""
        # Load cogs
        await self.load_extension('bot.cogs.dice')
        await self.load_extension('bot.cogs.characters')
        await self.load_extension('bot.cogs.campaigns')
        await self.load_extension('bot.cogs.source_material')
        await self.load_extension('bot.cogs.gm_tools')
        
        logger.info(f'Loaded {len(self.cogs)} cogs')
        
    async def on_ready(self):
        """Called when the bot is ready"""
        logger.info(f'{self.user} has connected to Discord!')
        logger.info(f'Bot is in {len(self.guilds)} guilds')
        
        # Sync slash commands
        try:
            synced = await self.tree.sync()
            logger.info(f'Synced {len(synced)} command(s)')
        except Exception as e:
            logger.error(f'Failed to sync commands: {e}')
            
    async def on_guild_join(self, guild):
        """Called when the bot joins a new guild"""
        logger.info(f'Joined guild: {guild.name} (ID: {guild.id})')
        
        # Create or update guild in database
        try:
            from django.contrib.auth.models import User

            # Create Django user first
            django_user, _ = User.objects.get_or_create(
                username=f"discord_{guild.owner.id}",
                defaults={
                    'email': f"discord_{guild.owner.id}@example.com"
                }
            )

            owner_discord_user, _ = DiscordUser.objects.get_or_create(
                discord_id=str(guild.owner.id),
                defaults={
                    'user': django_user,
                    'discord_username': guild.owner.name,
                    'discord_discriminator': guild.owner.discriminator or '0000',
                    'avatar_url': str(guild.owner.avatar.url) if guild.owner.avatar else ''
                }
            )
            
            guild_obj, created = Guild.objects.get_or_create(
                guild_id=str(guild.id),
                defaults={
                    'name': guild.name,
                    'icon_url': str(guild.icon.url) if guild.icon else '',
                    'owner': owner_discord_user
                }
            )
            
            if created:
                logger.info(f'Created new guild record for {guild.name}')
            else:
                logger.info(f'Updated existing guild record for {guild.name}')
                
        except Exception as e:
            logger.error(f'Error creating guild record: {e}')
            
    async def on_member_join(self, member):
        """Called when a member joins a guild"""
        if member.bot:
            return
            
        # Create or update user in database
        try:
            from django.contrib.auth.models import User

            # Create Django user first
            django_user, _ = User.objects.get_or_create(
                username=f"discord_{member.id}",
                defaults={
                    'email': f"discord_{member.id}@example.com"
                }
            )

            discord_user, created = DiscordUser.objects.get_or_create(
                discord_id=str(member.id),
                defaults={
                    'user': django_user,
                    'discord_username': member.name,
                    'discord_discriminator': member.discriminator or '0000',
                    'avatar_url': str(member.avatar.url) if member.avatar else ''
                }
            )
            
            if created:
                logger.info(f'Created new user record for {member.name}')
                
        except Exception as e:
            logger.error(f'Error creating user record: {e}')

# Create bot instance
bot = DaggerfallBot()

@bot.command(name='help')
async def help_command(ctx):
    """Show help information"""
    embed = discord.Embed(
        title="Daggerheart Master Bot Commands",
        description="Your companion for Daggerheart RPG sessions",
        color=0x8B4513
    )
    
    embed.add_field(
        name="🎲 Dice Commands",
        value="`!dh roll <dice>` - Roll dice (e.g., 2d12+3)\n"
              "`!dh action` - Roll action dice (2d12)\n"
              "`!dh damage <dice>` - Roll damage dice",
        inline=False
    )
    
    embed.add_field(
        name="👤 Character Commands",
        value="`!dh character create` - Create a new character\n"
              "`!dh character show` - Show your character\n"
              "`!dh character list` - List your characters",
        inline=False
    )
    
    embed.add_field(
        name="🏰 Campaign Commands",
        value="`!dh campaign create` - Create a new campaign\n"
              "`!dh campaign join <name>` - Join a campaign\n"
              "`!dh campaign info` - Show campaign info",
        inline=False
    )
    
    embed.add_field(
        name="📚 Source Material",
        value="`!dh lookup <term>` - Search source material\n"
              "`!dh spell <name>` - Look up a spell\n"
              "`!dh class <name>` - Look up a class",
        inline=False
    )
    
    embed.add_field(
        name="🎭 GM Tools",
        value="`!dh npc generate` - Generate random NPC\n"
              "`!dh encounter` - Generate encounter\n"
              "`!dh session start` - Start a session",
        inline=False
    )
    
    embed.set_footer(text="Use !dh <command> help for detailed command help")
    
    await ctx.send(embed=embed)

if __name__ == '__main__':
    # Run the bot
    token = os.getenv('DISCORD_TOKEN')
    if not token:
        logger.error('DISCORD_TOKEN not found in environment variables')
        exit(1)
        
    bot.run(token)
