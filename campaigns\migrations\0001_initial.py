# Generated by Django 5.2.3 on 2025-06-15 00:26

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('core', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Campaign',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('max_players', models.IntegerField(default=6)),
                ('session_channel_id', models.CharField(blank=True, max_length=20)),
                ('notes_channel_id', models.CharField(blank=True, max_length=20)),
                ('game_master', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='gm_campaigns', to='core.discorduser')),
                ('guild', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.guild')),
            ],
        ),
        migrations.CreateModel(
            name='CampaignMembership',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('joined_at', models.DateTimeField(auto_now_add=True)),
                ('is_active', models.BooleanField(default=True)),
                ('campaign', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='campaigns.campaign')),
                ('player', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.discorduser')),
            ],
            options={
                'unique_together': {('campaign', 'player')},
            },
        ),
        migrations.AddField(
            model_name='campaign',
            name='players',
            field=models.ManyToManyField(related_name='player_campaigns', through='campaigns.CampaignMembership', to='core.discorduser'),
        ),
        migrations.CreateModel(
            name='Location',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('location_type', models.CharField(blank=True, max_length=50)),
                ('notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('campaign', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='locations', to='campaigns.campaign')),
                ('parent_location', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='campaigns.location')),
            ],
        ),
        migrations.CreateModel(
            name='NPC',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('ancestry', models.CharField(blank=True, max_length=100)),
                ('class_name', models.CharField(blank=True, max_length=100)),
                ('level', models.IntegerField(default=1)),
                ('agility', models.IntegerField(default=0)),
                ('strength', models.IntegerField(default=0)),
                ('finesse', models.IntegerField(default=0)),
                ('instinct', models.IntegerField(default=0)),
                ('presence', models.IntegerField(default=0)),
                ('knowledge', models.IntegerField(default=0)),
                ('hit_points', models.IntegerField(default=10)),
                ('current_hp', models.IntegerField(default=10)),
                ('notes', models.TextField(blank=True)),
                ('is_alive', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('campaign', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='npcs', to='campaigns.campaign')),
                ('location', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='campaigns.location')),
            ],
        ),
        migrations.CreateModel(
            name='Session',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_number', models.IntegerField()),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('scheduled_date', models.DateTimeField()),
                ('actual_start', models.DateTimeField(blank=True, null=True)),
                ('actual_end', models.DateTimeField(blank=True, null=True)),
                ('notes', models.TextField(blank=True)),
                ('status', models.CharField(choices=[('scheduled', 'Scheduled'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], default='scheduled', max_length=20)),
                ('campaign', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sessions', to='campaigns.campaign')),
            ],
            options={
                'ordering': ['campaign', 'session_number'],
                'unique_together': {('campaign', 'session_number')},
            },
        ),
    ]
