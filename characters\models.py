from django.db import models
from core.models import DiscordUser
from campaigns.models import Campaign


class Character(models.Model):
    """Player Character model based on Daggerheart rules"""
    # Basic Info
    player = models.ForeignKey(DiscordUser, on_delete=models.CASCADE, related_name='characters')
    campaign = models.ForeignKey(Campaign, on_delete=models.CASCADE, related_name='characters')
    name = models.CharField(max_length=200)
    ancestry = models.CharField(max_length=100)
    community = models.CharField(max_length=100, blank=True)
    class_name = models.CharField(max_length=100)
    subclass = models.CharField(max_length=100, blank=True)
    level = models.IntegerField(default=1)

    # Core Stats
    agility = models.IntegerField(default=0)
    strength = models.IntegerField(default=0)
    finesse = models.IntegerField(default=0)
    instinct = models.IntegerField(default=0)
    presence = models.IntegerField(default=0)
    knowledge = models.IntegerField(default=0)

    # Health and Resources
    hit_points = models.IntegerField(default=10)
    current_hp = models.IntegerField(default=10)
    stress = models.IntegerField(default=0)
    hope = models.IntegerField(default=2)
    fear = models.IntegerField(default=0)

    # Thresholds
    minor_threshold = models.IntegerField(default=5)
    major_threshold = models.IntegerField(default=10)
    severe_threshold = models.IntegerField(default=15)

    # Character Details
    background = models.TextField(blank=True)
    personality = models.TextField(blank=True)
    connections = models.TextField(blank=True)

    # Status
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} ({self.player.discord_username})"

    class Meta:
        unique_together = ('player', 'campaign', 'name')


class CharacterAbility(models.Model):
    """Character abilities and features"""
    character = models.ForeignKey(Character, on_delete=models.CASCADE, related_name='abilities')
    name = models.CharField(max_length=200)
    description = models.TextField()
    ability_type = models.CharField(max_length=50)  # class, ancestry, domain, etc.
    source = models.CharField(max_length=100)  # where it comes from
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return f"{self.character.name} - {self.name}"


class CharacterEquipment(models.Model):
    """Character equipment and inventory"""
    character = models.ForeignKey(Character, on_delete=models.CASCADE, related_name='equipment')
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    item_type = models.CharField(max_length=50)  # weapon, armor, tool, etc.
    quantity = models.IntegerField(default=1)
    is_equipped = models.BooleanField(default=False)

    # Weapon stats (if applicable)
    damage_die = models.CharField(max_length=20, blank=True)  # e.g., "d6", "d8"
    weapon_tags = models.CharField(max_length=200, blank=True)

    # Armor stats (if applicable)
    armor_value = models.IntegerField(default=0)

    def __str__(self):
        return f"{self.character.name} - {self.name}"


class CharacterSpell(models.Model):
    """Character spells and magical abilities"""
    character = models.ForeignKey(Character, on_delete=models.CASCADE, related_name='spells')
    name = models.CharField(max_length=200)
    description = models.TextField()
    spell_level = models.IntegerField(default=1)
    domain = models.CharField(max_length=100)
    casting_time = models.CharField(max_length=100)
    range_distance = models.CharField(max_length=100)
    duration = models.CharField(max_length=100)
    components = models.CharField(max_length=200, blank=True)

    def __str__(self):
        return f"{self.character.name} - {self.name}"


class CharacterNote(models.Model):
    """Character notes and journal entries"""
    character = models.ForeignKey(Character, on_delete=models.CASCADE, related_name='notes')
    title = models.CharField(max_length=200)
    content = models.TextField()
    note_type = models.CharField(max_length=50, default='general')  # general, session, relationship, etc.
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.character.name} - {self.title}"

    class Meta:
        ordering = ['-created_at']
