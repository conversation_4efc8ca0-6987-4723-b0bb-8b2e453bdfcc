from django.db import models
from django.utils import timezone
from core.models import DiscordUser, Guild


class Campaign(models.Model):
    """Campaign model for managing game sessions"""
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    guild = models.ForeignKey(Guild, on_delete=models.CASCADE)
    game_master = models.ForeignKey(DiscordUser, on_delete=models.CASCADE, related_name='gm_campaigns')
    players = models.ManyToManyField(DiscordUser, through='CampaignMembership', related_name='player_campaigns')
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Campaign settings
    max_players = models.IntegerField(default=6)
    session_channel_id = models.CharField(max_length=20, blank=True)
    notes_channel_id = models.CharField(max_length=20, blank=True)

    def __str__(self):
        return f"{self.name} (GM: {self.game_master})"


class CampaignMembership(models.Model):
    """Through model for Campaign-Player relationship"""
    campaign = models.ForeignKey(Campaign, on_delete=models.CASCADE)
    player = models.ForeignKey(DiscordUser, on_delete=models.CASCADE)
    joined_at = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        unique_together = ('campaign', 'player')


class Session(models.Model):
    """Game session model"""
    campaign = models.ForeignKey(Campaign, on_delete=models.CASCADE, related_name='sessions')
    session_number = models.IntegerField()
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    scheduled_date = models.DateTimeField()
    actual_start = models.DateTimeField(null=True, blank=True)
    actual_end = models.DateTimeField(null=True, blank=True)
    notes = models.TextField(blank=True)

    STATUS_CHOICES = [
        ('scheduled', 'Scheduled'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='scheduled')

    def __str__(self):
        return f"{self.campaign.name} - Session {self.session_number}: {self.title}"

    class Meta:
        unique_together = ('campaign', 'session_number')
        ordering = ['campaign', 'session_number']


class Location(models.Model):
    """Campaign locations/places"""
    campaign = models.ForeignKey(Campaign, on_delete=models.CASCADE, related_name='locations')
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    location_type = models.CharField(max_length=50, blank=True)  # city, dungeon, wilderness, etc.
    parent_location = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True)
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.name


class NPC(models.Model):
    """Non-Player Character model"""
    campaign = models.ForeignKey(Campaign, on_delete=models.CASCADE, related_name='npcs')
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    ancestry = models.CharField(max_length=100, blank=True)
    class_name = models.CharField(max_length=100, blank=True)
    level = models.IntegerField(default=1)

    # Stats
    agility = models.IntegerField(default=0)
    strength = models.IntegerField(default=0)
    finesse = models.IntegerField(default=0)
    instinct = models.IntegerField(default=0)
    presence = models.IntegerField(default=0)
    knowledge = models.IntegerField(default=0)

    # Health
    hit_points = models.IntegerField(default=10)
    current_hp = models.IntegerField(default=10)

    # Relationships
    location = models.ForeignKey(Location, on_delete=models.SET_NULL, null=True, blank=True)
    notes = models.TextField(blank=True)
    is_alive = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.name
