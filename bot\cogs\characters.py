import discord
from discord.ext import commands
from core.models import DiscordUser
from campaigns.models import Campaign
from characters.models import Character


class CharacterCog(commands.Cog):
    """Character management commands"""
    
    def __init__(self, bot):
        self.bot = bot

    async def get_or_create_discord_user(self, user):
        """Get or create DiscordUser from Discord user"""
        discord_user, created = DiscordUser.objects.get_or_create(
            discord_id=str(user.id),
            defaults={
                'discord_username': user.name,
                'discord_discriminator': user.discriminator or '0000',
                'avatar_url': str(user.avatar.url) if user.avatar else ''
            }
        )
        return discord_user

    @commands.group(name='character', aliases=['char'])
    async def character_group(self, ctx):
        """Character management commands"""
        if ctx.invoked_subcommand is None:
            await ctx.send("Use `!dh character <subcommand>`. Available: create, show, list, edit")

    @character_group.command(name='create')
    async def create_character(self, ctx):
        """Create a new character interactively"""
        discord_user = await self.get_or_create_discord_user(ctx.author)
        
        # Check if user is in any campaigns
        campaigns = Campaign.objects.filter(
            players=discord_user,
            is_active=True
        )
        
        if not campaigns.exists():
            embed = discord.Embed(
                title="❌ No Active Campaigns",
                description="You need to join a campaign before creating a character. Ask your GM to invite you!",
                color=0xFF0000
            )
            await ctx.send(embed=embed)
            return
            
        # If multiple campaigns, let user choose
        if campaigns.count() > 1:
            embed = discord.Embed(
                title="🏰 Select Campaign",
                description="Which campaign is this character for?",
                color=0x8B4513
            )
            
            campaign_list = ""
            for i, campaign in enumerate(campaigns, 1):
                campaign_list += f"{i}. {campaign.name}\n"
                
            embed.add_field(name="Campaigns", value=campaign_list, inline=False)
            await ctx.send(embed=embed)
            
            def check(m):
                return m.author == ctx.author and m.channel == ctx.channel
                
            try:
                msg = await self.bot.wait_for('message', check=check, timeout=30.0)
                campaign_index = int(msg.content) - 1
                if campaign_index < 0 or campaign_index >= campaigns.count():
                    raise ValueError
                campaign = list(campaigns)[campaign_index]
            except (ValueError, asyncio.TimeoutError):
                await ctx.send("❌ Invalid selection or timeout. Character creation cancelled.")
                return
        else:
            campaign = campaigns.first()
            
        # Start character creation process
        embed = discord.Embed(
            title="✨ Character Creation",
            description=f"Creating character for campaign: **{campaign.name}**\n\nWhat's your character's name?",
            color=0x8B4513
        )
        await ctx.send(embed=embed)
        
        # Get character name
        try:
            msg = await self.bot.wait_for('message', check=lambda m: m.author == ctx.author and m.channel == ctx.channel, timeout=60.0)
            character_name = msg.content.strip()
            
            if len(character_name) > 200:
                await ctx.send("❌ Character name too long! Please keep it under 200 characters.")
                return
                
            # Check if character name already exists for this user in this campaign
            if Character.objects.filter(player=discord_user, campaign=campaign, name=character_name).exists():
                await ctx.send(f"❌ You already have a character named '{character_name}' in this campaign!")
                return
                
        except asyncio.TimeoutError:
            await ctx.send("❌ Timeout. Character creation cancelled.")
            return
            
        # Get ancestry
        embed = discord.Embed(
            title="🧬 Character Ancestry",
            description=f"What's {character_name}'s ancestry? (e.g., Human, Elf, Dwarf, Halfling, etc.)",
            color=0x8B4513
        )
        await ctx.send(embed=embed)
        
        try:
            msg = await self.bot.wait_for('message', check=lambda m: m.author == ctx.author and m.channel == ctx.channel, timeout=60.0)
            ancestry = msg.content.strip()
        except asyncio.TimeoutError:
            await ctx.send("❌ Timeout. Character creation cancelled.")
            return
            
        # Get class
        embed = discord.Embed(
            title="⚔️ Character Class",
            description=f"What's {character_name}'s class? (e.g., Guardian, Seraph, Ranger, Wizard, etc.)",
            color=0x8B4513
        )
        await ctx.send(embed=embed)
        
        try:
            msg = await self.bot.wait_for('message', check=lambda m: m.author == ctx.author and m.channel == ctx.channel, timeout=60.0)
            character_class = msg.content.strip()
        except asyncio.TimeoutError:
            await ctx.send("❌ Timeout. Character creation cancelled.")
            return
            
        # Create the character with default stats
        character = Character.objects.create(
            player=discord_user,
            campaign=campaign,
            name=character_name,
            ancestry=ancestry,
            class_name=character_class,
            level=1,
            # Default stats - can be customized later
            agility=0,
            strength=0,
            finesse=0,
            instinct=0,
            presence=0,
            knowledge=0,
            hit_points=10,
            current_hp=10,
            hope=2,
            fear=0
        )
        
        # Success message
        embed = discord.Embed(
            title="✅ Character Created!",
            description=f"**{character_name}** has been created successfully!",
            color=0x00FF00
        )
        
        embed.add_field(name="Campaign", value=campaign.name, inline=True)
        embed.add_field(name="Ancestry", value=ancestry, inline=True)
        embed.add_field(name="Class", value=character_class, inline=True)
        embed.add_field(name="Level", value="1", inline=True)
        embed.add_field(name="Hit Points", value="10/10", inline=True)
        embed.add_field(name="Hope", value="2", inline=True)
        
        embed.set_footer(text="Use '!dh character edit' to customize stats and details")
        
        await ctx.send(embed=embed)

    @character_group.command(name='show')
    async def show_character(self, ctx, *, character_name: str = None):
        """Show character details"""
        discord_user = await self.get_or_create_discord_user(ctx.author)
        
        if character_name:
            try:
                character = Character.objects.get(
                    player=discord_user,
                    name__iexact=character_name,
                    is_active=True
                )
            except Character.DoesNotExist:
                await ctx.send(f"❌ Character '{character_name}' not found!")
                return
        else:
            # Show most recently created character
            character = Character.objects.filter(
                player=discord_user,
                is_active=True
            ).order_by('-created_at').first()
            
            if not character:
                await ctx.send("❌ No characters found! Use `!dh character create` to make one.")
                return
                
        # Create character sheet embed
        embed = discord.Embed(
            title=f"📋 {character.name}",
            description=f"Level {character.level} {character.ancestry} {character.class_name}",
            color=0x8B4513
        )
        
        # Basic info
        embed.add_field(name="Campaign", value=character.campaign.name, inline=True)
        embed.add_field(name="HP", value=f"{character.current_hp}/{character.hit_points}", inline=True)
        embed.add_field(name="Hope/Fear", value=f"{character.hope}/{character.fear}", inline=True)
        
        # Stats
        stats_text = (
            f"**Agility:** {character.agility:+d}\n"
            f"**Strength:** {character.strength:+d}\n"
            f"**Finesse:** {character.finesse:+d}\n"
            f"**Instinct:** {character.instinct:+d}\n"
            f"**Presence:** {character.presence:+d}\n"
            f"**Knowledge:** {character.knowledge:+d}"
        )
        embed.add_field(name="📊 Stats", value=stats_text, inline=True)
        
        # Thresholds
        thresholds_text = (
            f"**Minor:** {character.minor_threshold}\n"
            f"**Major:** {character.major_threshold}\n"
            f"**Severe:** {character.severe_threshold}"
        )
        embed.add_field(name="🎯 Thresholds", value=thresholds_text, inline=True)
        
        # Background if available
        if character.background:
            embed.add_field(name="📖 Background", value=character.background[:1024], inline=False)
            
        embed.set_footer(text=f"Created {character.created_at.strftime('%m/%d/%Y')}")
        
        await ctx.send(embed=embed)

    @character_group.command(name='list')
    async def list_characters(self, ctx):
        """List all your characters"""
        discord_user = await self.get_or_create_discord_user(ctx.author)
        
        characters = Character.objects.filter(
            player=discord_user,
            is_active=True
        ).order_by('campaign__name', 'name')
        
        if not characters.exists():
            await ctx.send("❌ No characters found! Use `!dh character create` to make one.")
            return
            
        embed = discord.Embed(
            title=f"👥 {ctx.author.display_name}'s Characters",
            color=0x8B4513
        )
        
        current_campaign = None
        character_list = ""
        
        for character in characters:
            if current_campaign != character.campaign.name:
                if character_list:
                    embed.add_field(name=f"🏰 {current_campaign}", value=character_list, inline=False)
                current_campaign = character.campaign.name
                character_list = ""
                
            character_list += f"**{character.name}** - Level {character.level} {character.ancestry} {character.class_name}\n"
            
        # Add the last campaign
        if character_list:
            embed.add_field(name=f"🏰 {current_campaign}", value=character_list, inline=False)
            
        await ctx.send(embed=embed)


async def setup(bot):
    await bot.add_cog(CharacterCog(bot))
