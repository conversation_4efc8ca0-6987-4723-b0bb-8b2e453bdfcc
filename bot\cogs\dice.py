import random
import re
import discord
from discord.ext import commands
from core.models import DiscordUser, Guild, DiceRoll


class DiceCog(commands.Cog):
    """Dice rolling commands for Daggerheart"""
    
    def __init__(self, bot):
        self.bot = bot
        
    def parse_dice_expression(self, expression):
        """Parse dice expression like '2d12+3' or 'd20'"""
        # Remove spaces and convert to lowercase
        expr = expression.replace(' ', '').lower()
        
        # Pattern for dice notation: [number]d[sides][+/-modifier]
        pattern = r'^(\d*)d(\d+)([+-]\d+)?$'
        match = re.match(pattern, expr)
        
        if not match:
            return None
            
        num_dice = int(match.group(1)) if match.group(1) else 1
        die_size = int(match.group(2))
        modifier = int(match.group(3)) if match.group(3) else 0
        
        # Validate reasonable limits
        if num_dice > 20 or die_size > 100 or num_dice < 1 or die_size < 2:
            return None
            
        return num_dice, die_size, modifier
        
    def roll_dice(self, num_dice, die_size, modifier=0):
        """Roll dice and return results"""
        rolls = [random.randint(1, die_size) for _ in range(num_dice)]
        total = sum(rolls) + modifier
        return rolls, total, modifier
        
    async def save_roll(self, user_id, guild_id, roll_type, expression, result, individual_rolls, modifier, description=""):
        """Save roll to database"""
        try:
            discord_user, _ = DiscordUser.objects.get_or_create(
                discord_id=str(user_id),
                defaults={'discord_username': 'Unknown', 'discord_discriminator': '0000'}
            )
            
            guild_obj = None
            if guild_id:
                guild_obj, _ = Guild.objects.get_or_create(
                    guild_id=str(guild_id),
                    defaults={'name': 'Unknown Guild', 'owner': discord_user}
                )
            
            DiceRoll.objects.create(
                user=discord_user,
                guild=guild_obj,
                roll_type=roll_type,
                dice_expression=expression,
                result=result,
                individual_rolls=individual_rolls,
                modifier=modifier,
                description=description
            )
        except Exception as e:
            print(f"Error saving roll: {e}")

    @commands.command(name='roll')
    async def roll_dice_command(self, ctx, *, dice_expression: str):
        """Roll dice with standard notation (e.g., 2d12+3, d20, 4d6-1)"""
        parsed = self.parse_dice_expression(dice_expression)
        if not parsed:
            await ctx.send("❌ Invalid dice expression! Use format like: `2d12+3`, `d20`, `4d6-1`")
            return
            
        num_dice, die_size, modifier = parsed
        rolls, total, mod = self.roll_dice(num_dice, die_size, modifier)
        
        # Create embed for the result
        embed = discord.Embed(
            title="🎲 Dice Roll",
            color=0x8B4513
        )
        
        embed.add_field(
            name="Expression",
            value=f"`{dice_expression}`",
            inline=True
        )
        
        embed.add_field(
            name="Result",
            value=f"**{total}**",
            inline=True
        )
        
        # Show individual rolls if more than one die
        if num_dice > 1:
            rolls_str = " + ".join(map(str, rolls))
            if modifier != 0:
                rolls_str += f" {'+' if modifier > 0 else ''}{modifier}"
            embed.add_field(
                name="Breakdown",
                value=f"({rolls_str}) = {total}",
                inline=False
            )
        elif modifier != 0:
            embed.add_field(
                name="Breakdown",
                value=f"({rolls[0]} {'+' if modifier > 0 else ''}{modifier}) = {total}",
                inline=False
            )
            
        embed.set_footer(text=f"Rolled by {ctx.author.display_name}")
        
        await ctx.send(embed=embed)
        
        # Save to database
        await self.save_roll(
            ctx.author.id,
            ctx.guild.id if ctx.guild else None,
            'custom',
            dice_expression,
            total,
            rolls,
            modifier
        )

    @commands.command(name='action')
    async def action_roll(self, ctx, modifier: int = 0):
        """Roll action dice (2d12) with optional modifier"""
        rolls, total, mod = self.roll_dice(2, 12, modifier)
        
        # Determine result type based on Daggerheart rules
        result_type = "Success"
        if 1 in rolls:
            if rolls.count(1) == 2:
                result_type = "Critical Failure (Fear)"
            else:
                result_type = "Failure with Hope"
        elif 12 in rolls:
            if rolls.count(12) == 2:
                result_type = "Critical Success"
            else:
                result_type = "Success with Hope"
                
        # Create embed
        embed = discord.Embed(
            title="⚔️ Action Roll",
            color=0x8B4513
        )
        
        embed.add_field(
            name="Dice Result",
            value=f"**{total}** ({rolls[0]} + {rolls[1]}{f' + {modifier}' if modifier != 0 else ''})",
            inline=False
        )
        
        embed.add_field(
            name="Outcome",
            value=f"**{result_type}**",
            inline=False
        )
        
        # Add special results explanation
        if "Hope" in result_type:
            embed.add_field(
                name="💫 Hope Token",
                value="You gain a Hope token!",
                inline=True
            )
        elif "Fear" in result_type:
            embed.add_field(
                name="😰 Fear Token",
                value="The GM gains a Fear token!",
                inline=True
            )
            
        embed.set_footer(text=f"Rolled by {ctx.author.display_name}")
        
        await ctx.send(embed=embed)
        
        # Save to database
        await self.save_roll(
            ctx.author.id,
            ctx.guild.id if ctx.guild else None,
            'action',
            f"2d12{'+' + str(modifier) if modifier > 0 else str(modifier) if modifier < 0 else ''}",
            total,
            rolls,
            modifier,
            result_type
        )

    @commands.command(name='damage')
    async def damage_roll(self, ctx, *, dice_expression: str):
        """Roll damage dice"""
        parsed = self.parse_dice_expression(dice_expression)
        if not parsed:
            await ctx.send("❌ Invalid dice expression! Use format like: `d6`, `2d8+3`, `d12-1`")
            return
            
        num_dice, die_size, modifier = parsed
        rolls, total, mod = self.roll_dice(num_dice, die_size, modifier)
        
        embed = discord.Embed(
            title="💥 Damage Roll",
            color=0xDC143C
        )
        
        embed.add_field(
            name="Damage",
            value=f"**{total}** damage",
            inline=True
        )
        
        if num_dice > 1 or modifier != 0:
            rolls_str = " + ".join(map(str, rolls))
            if modifier != 0:
                rolls_str += f" {'+' if modifier > 0 else ''}{modifier}"
            embed.add_field(
                name="Breakdown",
                value=f"({rolls_str}) = {total}",
                inline=False
            )
            
        embed.set_footer(text=f"Rolled by {ctx.author.display_name}")
        
        await ctx.send(embed=embed)
        
        # Save to database
        await self.save_roll(
            ctx.author.id,
            ctx.guild.id if ctx.guild else None,
            'damage',
            dice_expression,
            total,
            rolls,
            modifier
        )

    @commands.command(name='history')
    async def roll_history(self, ctx, limit: int = 5):
        """Show recent roll history"""
        if limit > 20:
            limit = 20
            
        try:
            discord_user = DiscordUser.objects.get(discord_id=str(ctx.author.id))
            recent_rolls = DiceRoll.objects.filter(user=discord_user).order_by('-timestamp')[:limit]
            
            if not recent_rolls:
                await ctx.send("No roll history found!")
                return
                
            embed = discord.Embed(
                title=f"🎲 Recent Rolls for {ctx.author.display_name}",
                color=0x8B4513
            )
            
            for roll in recent_rolls:
                timestamp = roll.timestamp.strftime("%m/%d %H:%M")
                embed.add_field(
                    name=f"{roll.dice_expression} = {roll.result}",
                    value=f"{roll.roll_type.title()} • {timestamp}",
                    inline=True
                )
                
            await ctx.send(embed=embed)
            
        except DiscordUser.DoesNotExist:
            await ctx.send("No roll history found!")


async def setup(bot):
    await bot.add_cog(DiceCog(bot))
