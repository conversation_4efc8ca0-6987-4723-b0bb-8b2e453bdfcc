from django.contrib import admin
from .models import (
    SourceDocument, ContentSection, Ancestry, CharacterClass, Subclass,
    Spell, Equipment, Rule
)


@admin.register(SourceDocument)
class SourceDocumentAdmin(admin.ModelAdmin):
    list_display = ['title', 'document_type', 'file_size', 'is_processed', 'upload_date']
    list_filter = ['document_type', 'is_processed', 'upload_date']
    search_fields = ['title', 'description']
    readonly_fields = ['file_size', 'upload_date', 'last_processed']


@admin.register(ContentSection)
class ContentSectionAdmin(admin.ModelAdmin):
    list_display = ['title', 'document', 'section_type', 'page_number']
    list_filter = ['section_type', 'document']
    search_fields = ['title', 'content', 'keywords']


@admin.register(Ancestry)
class AncestryAdmin(admin.ModelAdmin):
    list_display = ['name', 'size', 'speed']
    search_fields = ['name', 'description']


class SubclassInline(admin.TabularInline):
    model = Subclass
    extra = 0


@admin.register(CharacterClass)
class CharacterClassAdmin(admin.ModelAdmin):
    list_display = ['name', 'primary_stats', 'hit_points_base', 'hit_points_per_level']
    search_fields = ['name', 'description']
    inlines = [SubclassInline]


@admin.register(Spell)
class SpellAdmin(admin.ModelAdmin):
    list_display = ['name', 'level', 'domain', 'casting_time', 'range_distance']
    list_filter = ['level', 'domain']
    search_fields = ['name', 'description']


@admin.register(Equipment)
class EquipmentAdmin(admin.ModelAdmin):
    list_display = ['name', 'item_type', 'cost', 'damage_die', 'armor_value']
    list_filter = ['item_type']
    search_fields = ['name', 'description']


@admin.register(Rule)
class RuleAdmin(admin.ModelAdmin):
    list_display = ['title', 'category']
    list_filter = ['category']
    search_fields = ['title', 'content', 'tags']
