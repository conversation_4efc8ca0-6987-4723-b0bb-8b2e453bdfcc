version: '3.8'

services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: daggerfall_db
      POSTGRES_USER: daggerfall_user
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    restart: unless-stopped

  web:
    build: .
    command: python manage.py runserver 0.0.0.0:8000
    volumes:
      - .:/app
    ports:
      - "8000:8000"
    depends_on:
      - postgres
      - redis
    environment:
      - DATABASE_URL=***************************************************/daggerfall_db
      - REDIS_URL=redis://redis:6379/0

volumes:
  postgres_data:
