# Generated by Django 5.2.3 on 2025-06-15 00:26

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('campaigns', '0001_initial'),
        ('core', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Character',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('ancestry', models.CharField(max_length=100)),
                ('community', models.CharField(blank=True, max_length=100)),
                ('class_name', models.CharField(max_length=100)),
                ('subclass', models.CharField(blank=True, max_length=100)),
                ('level', models.IntegerField(default=1)),
                ('agility', models.IntegerField(default=0)),
                ('strength', models.IntegerField(default=0)),
                ('finesse', models.IntegerField(default=0)),
                ('instinct', models.IntegerField(default=0)),
                ('presence', models.IntegerField(default=0)),
                ('knowledge', models.IntegerField(default=0)),
                ('hit_points', models.IntegerField(default=10)),
                ('current_hp', models.IntegerField(default=10)),
                ('stress', models.IntegerField(default=0)),
                ('hope', models.IntegerField(default=2)),
                ('fear', models.IntegerField(default=0)),
                ('minor_threshold', models.IntegerField(default=5)),
                ('major_threshold', models.IntegerField(default=10)),
                ('severe_threshold', models.IntegerField(default=15)),
                ('background', models.TextField(blank=True)),
                ('personality', models.TextField(blank=True)),
                ('connections', models.TextField(blank=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('campaign', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='characters', to='campaigns.campaign')),
                ('player', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='characters', to='core.discorduser')),
            ],
            options={
                'unique_together': {('player', 'campaign', 'name')},
            },
        ),
        migrations.CreateModel(
            name='CharacterAbility',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('ability_type', models.CharField(max_length=50)),
                ('source', models.CharField(max_length=100)),
                ('is_active', models.BooleanField(default=True)),
                ('character', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='abilities', to='characters.character')),
            ],
        ),
        migrations.CreateModel(
            name='CharacterEquipment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('item_type', models.CharField(max_length=50)),
                ('quantity', models.IntegerField(default=1)),
                ('is_equipped', models.BooleanField(default=False)),
                ('damage_die', models.CharField(blank=True, max_length=20)),
                ('weapon_tags', models.CharField(blank=True, max_length=200)),
                ('armor_value', models.IntegerField(default=0)),
                ('character', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='equipment', to='characters.character')),
            ],
        ),
        migrations.CreateModel(
            name='CharacterNote',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('content', models.TextField()),
                ('note_type', models.CharField(default='general', max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('character', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notes', to='characters.character')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CharacterSpell',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('spell_level', models.IntegerField(default=1)),
                ('domain', models.CharField(max_length=100)),
                ('casting_time', models.CharField(max_length=100)),
                ('range_distance', models.CharField(max_length=100)),
                ('duration', models.CharField(max_length=100)),
                ('components', models.CharField(blank=True, max_length=200)),
                ('character', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='spells', to='characters.character')),
            ],
        ),
    ]
