{% extends 'base.html' %}
{% load static %}

{% block title %}Dashboard - Daggerheart Master{% endblock %}

{% block content %}
<div class="row">
    <!-- Welcome Section -->
    <div class="col-12 mb-4">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h1 class="card-title mb-3">
                            <i class="fas fa-dice-d20 me-2"></i>
                            Welcome to Daggerheart Master
                        </h1>
                        <p class="card-text lead">
                            Your comprehensive companion for Daggerheart RPG sessions. 
                            Manage campaigns, characters, and access source material both on Discord and the web.
                        </p>
                        <div class="d-flex gap-2">
                            <span class="badge bg-light text-dark">
                                <i class="fab fa-discord me-1"></i>Discord Bot
                            </span>
                            <span class="badge bg-light text-dark">
                                <i class="fas fa-globe me-1"></i>Web Interface
                            </span>
                            <span class="badge bg-light text-dark">
                                <i class="fas fa-database me-1"></i>PostgreSQL
                            </span>
                        </div>
                    </div>
                    <div class="col-md-4 text-center">
                        <i class="fas fa-dragon display-1 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="col-md-3 mb-4">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-castle fa-2x text-primary mb-2"></i>
                <h5 class="card-title">Campaigns</h5>
                <h3 class="text-primary" id="campaign-count">-</h3>
                <p class="card-text text-muted">Active campaigns</p>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-4">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-user-friends fa-2x text-success mb-2"></i>
                <h5 class="card-title">Characters</h5>
                <h3 class="text-success" id="character-count">-</h3>
                <p class="card-text text-muted">Your characters</p>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-4">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-dice fa-2x text-warning mb-2"></i>
                <h5 class="card-title">Dice Rolls</h5>
                <h3 class="text-warning" id="roll-count">-</h3>
                <p class="card-text text-muted">Recent rolls</p>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-4">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-server fa-2x text-info mb-2"></i>
                <h5 class="card-title">Servers</h5>
                <h3 class="text-info" id="guild-count">-</h3>
                <p class="card-text text-muted">Discord servers</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Activity -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clock me-2"></i>Recent Activity
                </h5>
            </div>
            <div class="card-body">
                <div id="recent-activity">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2 text-muted">Loading recent activity...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-primary" onclick="showDiceRoller()">
                        <i class="fas fa-dice me-2"></i>Roll Dice
                    </button>
                    <button class="btn btn-success" onclick="showCharacterCreator()">
                        <i class="fas fa-user-plus me-2"></i>Create Character
                    </button>
                    <button class="btn btn-info" onclick="showSourceMaterial()">
                        <i class="fas fa-search me-2"></i>Search Rules
                    </button>
                    <button class="btn btn-warning" onclick="showCampaignManager()">
                        <i class="fas fa-castle me-2"></i>Manage Campaigns
                    </button>
                </div>
            </div>
        </div>

        <!-- Discord Bot Status -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fab fa-discord me-2"></i>Discord Bot
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="status-indicator bg-success me-2"></div>
                    <span>Bot is online and ready!</span>
                </div>
                <hr>
                <small class="text-muted">
                    <strong>Commands:</strong><br>
                    <code>!dh help</code> - Show all commands<br>
                    <code>!dh roll 2d12+3</code> - Roll dice<br>
                    <code>!dh character create</code> - Create character
                </small>
            </div>
        </div>
    </div>
</div>

<!-- Dice Roller Modal -->
<div class="modal fade" id="diceRollerModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-dice me-2"></i>Dice Roller
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="diceExpression" class="form-label">Dice Expression</label>
                    <input type="text" class="form-control" id="diceExpression" placeholder="2d12+3">
                    <div class="form-text">Examples: d20, 2d12+3, 4d6-1</div>
                </div>
                <div class="d-grid">
                    <button class="btn btn-primary" onclick="rollDice()">
                        <i class="fas fa-dice me-2"></i>Roll!
                    </button>
                </div>
                <div id="diceResult" class="mt-3"></div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Load dashboard data
document.addEventListener('DOMContentLoaded', function() {
    loadDashboardData();
});

async function loadDashboardData() {
    try {
        // Load stats (you'll need to implement these API endpoints)
        // For now, we'll use placeholder data
        document.getElementById('campaign-count').textContent = '0';
        document.getElementById('character-count').textContent = '0';
        document.getElementById('roll-count').textContent = '0';
        document.getElementById('guild-count').textContent = '0';
        
        // Load recent activity
        document.getElementById('recent-activity').innerHTML = `
            <div class="text-center py-4 text-muted">
                <i class="fas fa-info-circle me-2"></i>
                No recent activity. Start by creating a character or joining a campaign!
            </div>
        `;
    } catch (error) {
        console.error('Error loading dashboard data:', error);
    }
}

function showDiceRoller() {
    new bootstrap.Modal(document.getElementById('diceRollerModal')).show();
}

function showCharacterCreator() {
    alert('Character creator coming soon! Use the Discord bot for now: !dh character create');
}

function showSourceMaterial() {
    alert('Source material browser coming soon! Use the Discord bot for now: !dh lookup <term>');
}

function showCampaignManager() {
    alert('Campaign manager coming soon! Use the Discord bot for now: !dh campaign create');
}

function rollDice() {
    const expression = document.getElementById('diceExpression').value;
    if (!expression) {
        alert('Please enter a dice expression!');
        return;
    }
    
    // Simple client-side dice rolling for demo
    const result = Math.floor(Math.random() * 20) + 1;
    document.getElementById('diceResult').innerHTML = `
        <div class="alert alert-success">
            <strong>Result:</strong> ${result}
            <br><small>Expression: ${expression}</small>
        </div>
    `;
}
</script>
{% endblock %}
