import random
import discord
from discord.ext import commands
from django.utils import timezone
from core.models import DiscordUser
from campaigns.models import Campaign, NPC, Location, Session


class GMToolsCog(commands.Cog):
    """Game Master tools and utilities"""
    
    def __init__(self, bot):
        self.bot = bot
        
        # NPC generation data
        self.npc_names = {
            'human': ['<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>'],
            'elf': ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'],
            'dwarf': ['<PERSON><PERSON>', 'Dain', '<PERSON><PERSON>', '<PERSON>im<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>in', '<PERSON><PERSON>in'],
            'halfling': ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>']
        }
        
        self.npc_traits = [
            '<PERSON>', '<PERSON><PERSON><PERSON>', 'Curious', 'Su<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>ish',
            '<PERSON>rous', 'Greedy', 'Honest', 'Deceptive', 'Calm', 'Nervous', 'Confident', 'Insecure'
        ]
        
        self.npc_occupations = [
            'Merchant', 'Guard', 'Farmer', 'Blacksmith', 'Innkeeper', 'Scholar', 'Priest', 'Thief',
            'Noble', 'Soldier', 'Healer', 'Bard', 'Hunter', 'Sailor', 'Mage', 'Artisan'
        ]

    async def get_or_create_discord_user(self, user):
        """Get or create DiscordUser from Discord user"""
        discord_user, created = DiscordUser.objects.get_or_create(
            discord_id=str(user.id),
            defaults={
                'discord_username': user.name,
                'discord_discriminator': user.discriminator or '0000',
                'avatar_url': str(user.avatar.url) if user.avatar else ''
            }
        )
        return discord_user

    def is_gm(self, user_id, guild_id):
        """Check if user is a GM in any campaign in the guild"""
        try:
            discord_user = DiscordUser.objects.get(discord_id=str(user_id))
            return Campaign.objects.filter(
                game_master=discord_user,
                guild__guild_id=str(guild_id),
                is_active=True
            ).exists()
        except DiscordUser.DoesNotExist:
            return False

    @commands.group(name='gm')
    async def gm_group(self, ctx):
        """Game Master tools (GM only)"""
        if ctx.invoked_subcommand is None:
            await ctx.send("Use `!dh gm <subcommand>`. Available: npc, location, session, encounter")

    @gm_group.command(name='npc')
    async def generate_npc(self, ctx, ancestry: str = None):
        """Generate a random NPC"""
        if not ctx.guild or not self.is_gm(ctx.author.id, ctx.guild.id):
            await ctx.send("❌ This command is for Game Masters only!")
            return
            
        # Random ancestry if not specified
        if not ancestry:
            ancestry = random.choice(['human', 'elf', 'dwarf', 'halfling'])
        else:
            ancestry = ancestry.lower()
            if ancestry not in self.npc_names:
                ancestry = 'human'
                
        # Generate NPC
        name = random.choice(self.npc_names[ancestry])
        occupation = random.choice(self.npc_occupations)
        trait1 = random.choice(self.npc_traits)
        trait2 = random.choice([t for t in self.npc_traits if t != trait1])
        
        # Random stats
        level = random.randint(1, 5)
        stats = {
            'agility': random.randint(-2, 3),
            'strength': random.randint(-2, 3),
            'finesse': random.randint(-2, 3),
            'instinct': random.randint(-2, 3),
            'presence': random.randint(-2, 3),
            'knowledge': random.randint(-2, 3)
        }
        
        hp = 10 + (level - 1) * 5 + stats['strength']
        
        embed = discord.Embed(
            title=f"🎭 Generated NPC: {name}",
            color=0x8B4513
        )
        
        embed.add_field(name="Ancestry", value=ancestry.title(), inline=True)
        embed.add_field(name="Occupation", value=occupation, inline=True)
        embed.add_field(name="Level", value=str(level), inline=True)
        
        embed.add_field(name="Personality", value=f"{trait1}, {trait2}", inline=False)
        
        stats_text = "\n".join([f"**{stat.title()}:** {value:+d}" for stat, value in stats.items()])
        embed.add_field(name="Stats", value=stats_text, inline=True)
        
        embed.add_field(name="Hit Points", value=str(hp), inline=True)
        
        # Add save button for GMs
        embed.set_footer(text="Use '!dh gm save npc <name>' to save this NPC to your campaign")
        
        await ctx.send(embed=embed)

    @gm_group.command(name='location')
    async def generate_location(self, ctx, location_type: str = None):
        """Generate a random location"""
        if not ctx.guild or not self.is_gm(ctx.author.id, ctx.guild.id):
            await ctx.send("❌ This command is for Game Masters only!")
            return
            
        location_types = ['tavern', 'shop', 'temple', 'dungeon', 'forest', 'city', 'village', 'castle']
        
        if not location_type:
            location_type = random.choice(location_types)
        else:
            location_type = location_type.lower()
            if location_type not in location_types:
                location_type = random.choice(location_types)
                
        # Location name generators
        location_names = {
            'tavern': ['The Prancing Pony', 'The Golden Griffin', 'The Drunken Dragon', 'The Silver Stag'],
            'shop': ['Ye Olde Magic Shoppe', 'Ironworks & More', 'The Curious Curio', 'Potions & Provisions'],
            'temple': ['Temple of Light', 'Shrine of the Ancients', 'Cathedral of Hope', 'Sacred Grove'],
            'dungeon': ['The Forgotten Crypt', 'Shadowmere Caverns', 'The Lost Mine', 'Ruins of Eldara'],
            'forest': ['Whispering Woods', 'Darkwood Forest', 'The Enchanted Grove', 'Thornbriar Thicket'],
            'city': ['Goldenhaven', 'Ironhold', 'Silverbrook', 'Stormwatch'],
            'village': ['Millbrook', 'Oakenford', 'Rosehaven', 'Thornfield'],
            'castle': ['Castle Ravencrest', 'Ironhold Keep', 'Stormwind Fortress', 'Shadowmere Castle']
        }
        
        name = random.choice(location_names[location_type])
        
        # Generate description elements
        descriptions = {
            'tavern': ['bustling with travelers', 'dimly lit with flickering candles', 'filled with the aroma of roasted meat'],
            'shop': ['cluttered with mysterious items', 'well-organized and clean', 'dusty and filled with antiques'],
            'temple': ['peaceful and serene', 'grand with soaring columns', 'ancient and weathered'],
            'dungeon': ['dark and foreboding', 'filled with strange echoes', 'damp and musty'],
            'forest': ['dense with ancient trees', 'filled with mysterious sounds', 'dappled with sunlight'],
            'city': ['bustling with activity', 'well-fortified with high walls', 'prosperous and clean'],
            'village': ['quiet and peaceful', 'surrounded by farmland', 'quaint with cobblestone streets'],
            'castle': ['imposing and grand', 'well-defended with towers', 'ancient and mysterious']
        }
        
        description = random.choice(descriptions[location_type])
        
        embed = discord.Embed(
            title=f"🏰 Generated Location: {name}",
            description=f"A {location_type} that is {description}.",
            color=0x8B4513
        )
        
        embed.add_field(name="Type", value=location_type.title(), inline=True)
        
        # Add some random features
        features = [
            "Has a secret passage", "Guarded by magical wards", "Contains valuable treasure",
            "Inhabited by friendly NPCs", "Has a dark history", "Recently abandoned",
            "Under construction", "Partially ruined", "Well-maintained", "Mysteriously quiet"
        ]
        
        feature = random.choice(features)
        embed.add_field(name="Notable Feature", value=feature, inline=True)
        
        embed.set_footer(text="Use '!dh gm save location <name>' to save this location to your campaign")
        
        await ctx.send(embed=embed)

    @gm_group.command(name='encounter')
    async def generate_encounter(self, ctx, difficulty: str = 'medium'):
        """Generate a random encounter"""
        if not ctx.guild or not self.is_gm(ctx.author.id, ctx.guild.id):
            await ctx.send("❌ This command is for Game Masters only!")
            return
            
        difficulties = ['easy', 'medium', 'hard', 'deadly']
        if difficulty.lower() not in difficulties:
            difficulty = 'medium'
        else:
            difficulty = difficulty.lower()
            
        # Encounter types
        encounter_types = ['combat', 'social', 'exploration', 'puzzle']
        encounter_type = random.choice(encounter_types)
        
        embed = discord.Embed(
            title=f"⚔️ Generated Encounter ({difficulty.title()})",
            color=0x8B4513
        )
        
        embed.add_field(name="Type", value=encounter_type.title(), inline=True)
        embed.add_field(name="Difficulty", value=difficulty.title(), inline=True)
        
        if encounter_type == 'combat':
            enemies = ['Goblins', 'Bandits', 'Wild Animals', 'Undead', 'Cultists', 'Mercenaries']
            enemy = random.choice(enemies)
            
            # Number based on difficulty
            numbers = {'easy': '1-2', 'medium': '2-4', 'hard': '4-6', 'deadly': '6+'}
            number = numbers[difficulty]
            
            embed.add_field(name="Enemies", value=f"{number} {enemy}", inline=False)
            embed.add_field(name="Tactics", value="Use terrain to their advantage", inline=True)
            
        elif encounter_type == 'social':
            npcs = ['Merchant', 'Noble', 'Guard Captain', 'Priest', 'Informant', 'Rival Adventurer']
            npc = random.choice(npcs)
            
            goals = ['wants information', 'needs help', 'offers a deal', 'blocks the path', 'seeks revenge']
            goal = random.choice(goals)
            
            embed.add_field(name="NPC", value=npc, inline=False)
            embed.add_field(name="Goal", value=goal.title(), inline=True)
            
        elif encounter_type == 'exploration':
            obstacles = ['Trapped corridor', 'Collapsed bridge', 'Magical barrier', 'Hidden door', 'Dangerous terrain']
            obstacle = random.choice(obstacles)
            
            embed.add_field(name="Obstacle", value=obstacle, inline=False)
            embed.add_field(name="Solution", value="Requires creative thinking", inline=True)
            
        else:  # puzzle
            puzzles = ['Riddle door', 'Symbol sequence', 'Mechanical lock', 'Magic circle', 'Ancient script']
            puzzle = random.choice(puzzles)
            
            embed.add_field(name="Puzzle", value=puzzle, inline=False)
            embed.add_field(name="Hint", value="Look for clues in the environment", inline=True)
            
        await ctx.send(embed=embed)

    @gm_group.command(name='session')
    async def start_session(self, ctx, *, session_title: str = None):
        """Start a new game session"""
        if not ctx.guild or not self.is_gm(ctx.author.id, ctx.guild.id):
            await ctx.send("❌ This command is for Game Masters only!")
            return
            
        discord_user = await self.get_or_create_discord_user(ctx.author)
        
        # Get GM's campaigns
        campaigns = Campaign.objects.filter(
            game_master=discord_user,
            guild__guild_id=str(ctx.guild.id),
            is_active=True
        )
        
        if not campaigns.exists():
            await ctx.send("❌ You don't have any active campaigns!")
            return
            
        # For now, use the first campaign (could be expanded to choose)
        campaign = campaigns.first()
        
        if not session_title:
            session_title = f"Session {campaign.sessions.count() + 1}"
            
        # Create session
        session = Session.objects.create(
            campaign=campaign,
            session_number=campaign.sessions.count() + 1,
            title=session_title,
            status='in_progress',
            scheduled_date=timezone.now()
        )
        
        embed = discord.Embed(
            title="🎲 Session Started!",
            description=f"**{session_title}** has begun!",
            color=0x00FF00
        )
        
        embed.add_field(name="Campaign", value=campaign.name, inline=True)
        embed.add_field(name="Session #", value=str(session.session_number), inline=True)
        embed.add_field(name="Status", value="In Progress", inline=True)
        
        player_count = campaign.players.count()
        embed.add_field(name="Players", value=str(player_count), inline=True)
        
        embed.set_footer(text="Use '!dh gm session end' to end the session")
        
        await ctx.send(embed=embed)


async def setup(bot):
    await bot.add_cog(GMToolsCog(bot))
