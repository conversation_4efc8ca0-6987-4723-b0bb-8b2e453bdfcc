from django.contrib import admin
from .models import DiscordUser, Guild, GuildMembership, DiceRoll


@admin.register(DiscordUser)
class DiscordUserAdmin(admin.ModelAdmin):
    list_display = ['discord_username', 'discord_discriminator', 'discord_id', 'created_at']
    list_filter = ['created_at']
    search_fields = ['discord_username', 'discord_id']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(Guild)
class GuildAdmin(admin.ModelAdmin):
    list_display = ['name', 'guild_id', 'owner', 'bot_enabled', 'created_at']
    list_filter = ['bot_enabled', 'created_at']
    search_fields = ['name', 'guild_id']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(GuildMembership)
class GuildMembershipAdmin(admin.ModelAdmin):
    list_display = ['guild', 'user', 'is_admin', 'joined_at']
    list_filter = ['is_admin', 'joined_at']
    search_fields = ['guild__name', 'user__discord_username']


@admin.register(DiceRoll)
class DiceRollAdmin(admin.ModelAdmin):
    list_display = ['user', 'roll_type', 'dice_expression', 'result', 'timestamp']
    list_filter = ['roll_type', 'timestamp']
    search_fields = ['user__discord_username', 'dice_expression']
    readonly_fields = ['timestamp']
