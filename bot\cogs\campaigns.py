import discord
from discord.ext import commands
from core.models import DiscordUser, Guild
from campaigns.models import Campaign, CampaignMembership, Session


class CampaignCog(commands.Cog):
    """Campaign management commands"""
    
    def __init__(self, bot):
        self.bot = bot

    async def get_or_create_discord_user(self, user):
        """Get or create DiscordUser from Discord user"""
        discord_user, created = DiscordUser.objects.get_or_create(
            discord_id=str(user.id),
            defaults={
                'discord_username': user.name,
                'discord_discriminator': user.discriminator or '0000',
                'avatar_url': str(user.avatar.url) if user.avatar else ''
            }
        )
        return discord_user

    @commands.group(name='campaign', aliases=['camp'])
    async def campaign_group(self, ctx):
        """Campaign management commands"""
        if ctx.invoked_subcommand is None:
            await ctx.send("Use `!dh campaign <subcommand>`. Available: create, join, leave, info, list")

    @campaign_group.command(name='create')
    async def create_campaign(self, ctx, *, campaign_name: str):
        """Create a new campaign (GM only)"""
        if not ctx.guild:
            await ctx.send("❌ Campaigns can only be created in servers!")
            return
            
        discord_user = await self.get_or_create_discord_user(ctx.author)
        
        # Get or create guild
        guild_obj, _ = Guild.objects.get_or_create(
            guild_id=str(ctx.guild.id),
            defaults={
                'name': ctx.guild.name,
                'icon_url': str(ctx.guild.icon.url) if ctx.guild.icon else '',
                'owner': discord_user
            }
        )
        
        # Check if campaign name already exists in this guild
        if Campaign.objects.filter(guild=guild_obj, name__iexact=campaign_name).exists():
            await ctx.send(f"❌ A campaign named '{campaign_name}' already exists in this server!")
            return
            
        # Create the campaign
        campaign = Campaign.objects.create(
            name=campaign_name,
            guild=guild_obj,
            game_master=discord_user,
            description="",
            max_players=6
        )
        
        embed = discord.Embed(
            title="✅ Campaign Created!",
            description=f"**{campaign_name}** has been created successfully!",
            color=0x00FF00
        )
        
        embed.add_field(name="Game Master", value=ctx.author.display_name, inline=True)
        embed.add_field(name="Max Players", value="6", inline=True)
        embed.add_field(name="Status", value="Active", inline=True)
        
        embed.add_field(
            name="Next Steps",
            value="• Use `!dh campaign invite @player` to add players\n"
                  "• Use `!dh campaign info` to see campaign details\n"
                  "• Players can create characters with `!dh character create`",
            inline=False
        )
        
        await ctx.send(embed=embed)

    @campaign_group.command(name='invite')
    async def invite_player(self, ctx, member: discord.Member):
        """Invite a player to your campaign"""
        if not ctx.guild:
            await ctx.send("❌ This command can only be used in servers!")
            return
            
        discord_user = await self.get_or_create_discord_user(ctx.author)
        target_user = await self.get_or_create_discord_user(member)
        
        # Find campaigns where the user is GM
        gm_campaigns = Campaign.objects.filter(
            game_master=discord_user,
            guild__guild_id=str(ctx.guild.id),
            is_active=True
        )
        
        if not gm_campaigns.exists():
            await ctx.send("❌ You don't have any active campaigns in this server!")
            return
            
        # If multiple campaigns, let GM choose
        if gm_campaigns.count() > 1:
            embed = discord.Embed(
                title="🏰 Select Campaign",
                description="Which campaign do you want to invite the player to?",
                color=0x8B4513
            )
            
            campaign_list = ""
            for i, campaign in enumerate(gm_campaigns, 1):
                player_count = campaign.players.count()
                campaign_list += f"{i}. {campaign.name} ({player_count}/{campaign.max_players} players)\n"
                
            embed.add_field(name="Your Campaigns", value=campaign_list, inline=False)
            await ctx.send(embed=embed)
            
            def check(m):
                return m.author == ctx.author and m.channel == ctx.channel
                
            try:
                msg = await self.bot.wait_for('message', check=check, timeout=30.0)
                campaign_index = int(msg.content) - 1
                if campaign_index < 0 or campaign_index >= gm_campaigns.count():
                    raise ValueError
                campaign = list(gm_campaigns)[campaign_index]
            except (ValueError, asyncio.TimeoutError):
                await ctx.send("❌ Invalid selection or timeout. Invitation cancelled.")
                return
        else:
            campaign = gm_campaigns.first()
            
        # Check if player is already in the campaign
        if CampaignMembership.objects.filter(campaign=campaign, player=target_user).exists():
            await ctx.send(f"❌ {member.display_name} is already in the campaign '{campaign.name}'!")
            return
            
        # Check if campaign is full
        if campaign.players.count() >= campaign.max_players:
            await ctx.send(f"❌ Campaign '{campaign.name}' is full ({campaign.max_players} players)!")
            return
            
        # Add player to campaign
        CampaignMembership.objects.create(
            campaign=campaign,
            player=target_user,
            is_active=True
        )
        
        embed = discord.Embed(
            title="✅ Player Invited!",
            description=f"{member.display_name} has been added to **{campaign.name}**!",
            color=0x00FF00
        )
        
        player_count = campaign.players.count()
        embed.add_field(name="Players", value=f"{player_count}/{campaign.max_players}", inline=True)
        embed.add_field(name="Campaign", value=campaign.name, inline=True)
        
        await ctx.send(embed=embed)
        
        # Send DM to invited player
        try:
            dm_embed = discord.Embed(
                title="🎲 Campaign Invitation",
                description=f"You've been invited to join **{campaign.name}** by {ctx.author.display_name}!",
                color=0x8B4513
            )
            dm_embed.add_field(
                name="Next Steps",
                value="You can now create a character with `!dh character create` in the server.",
                inline=False
            )
            await member.send(embed=dm_embed)
        except discord.Forbidden:
            pass  # User has DMs disabled

    @campaign_group.command(name='info')
    async def campaign_info(self, ctx, *, campaign_name: str = None):
        """Show campaign information"""
        if not ctx.guild:
            await ctx.send("❌ This command can only be used in servers!")
            return
            
        discord_user = await self.get_or_create_discord_user(ctx.author)
        
        # Get guild
        try:
            guild_obj = Guild.objects.get(guild_id=str(ctx.guild.id))
        except Guild.DoesNotExist:
            await ctx.send("❌ No campaigns found in this server!")
            return
            
        if campaign_name:
            try:
                campaign = Campaign.objects.get(
                    guild=guild_obj,
                    name__iexact=campaign_name,
                    is_active=True
                )
            except Campaign.DoesNotExist:
                await ctx.send(f"❌ Campaign '{campaign_name}' not found!")
                return
        else:
            # Find user's campaign (as player or GM)
            campaigns = Campaign.objects.filter(
                guild=guild_obj,
                is_active=True
            ).filter(
                models.Q(game_master=discord_user) | models.Q(players=discord_user)
            ).distinct()
            
            if not campaigns.exists():
                await ctx.send("❌ You're not part of any campaigns in this server!")
                return
            elif campaigns.count() > 1:
                await ctx.send("❌ You're in multiple campaigns. Please specify: `!dh campaign info <campaign_name>`")
                return
            else:
                campaign = campaigns.first()
                
        # Create info embed
        embed = discord.Embed(
            title=f"🏰 {campaign.name}",
            description=campaign.description or "No description set",
            color=0x8B4513
        )
        
        embed.add_field(name="Game Master", value=campaign.game_master.discord_username, inline=True)
        
        player_count = campaign.players.count()
        embed.add_field(name="Players", value=f"{player_count}/{campaign.max_players}", inline=True)
        
        # List players
        if player_count > 0:
            players_list = []
            for membership in CampaignMembership.objects.filter(campaign=campaign, is_active=True):
                players_list.append(membership.player.discord_username)
            embed.add_field(name="Player List", value="\n".join(players_list), inline=False)
            
        # Session info
        session_count = Session.objects.filter(campaign=campaign).count()
        embed.add_field(name="Sessions", value=str(session_count), inline=True)
        
        embed.add_field(name="Status", value="Active" if campaign.is_active else "Inactive", inline=True)
        
        embed.set_footer(text=f"Created {campaign.created_at.strftime('%m/%d/%Y')}")
        
        await ctx.send(embed=embed)

    @campaign_group.command(name='list')
    async def list_campaigns(self, ctx):
        """List all campaigns in this server"""
        if not ctx.guild:
            await ctx.send("❌ This command can only be used in servers!")
            return
            
        try:
            guild_obj = Guild.objects.get(guild_id=str(ctx.guild.id))
        except Guild.DoesNotExist:
            await ctx.send("❌ No campaigns found in this server!")
            return
            
        campaigns = Campaign.objects.filter(guild=guild_obj, is_active=True)
        
        if not campaigns.exists():
            await ctx.send("❌ No active campaigns in this server!")
            return
            
        embed = discord.Embed(
            title=f"🏰 Campaigns in {ctx.guild.name}",
            color=0x8B4513
        )
        
        for campaign in campaigns:
            player_count = campaign.players.count()
            value = f"GM: {campaign.game_master.discord_username}\n"
            value += f"Players: {player_count}/{campaign.max_players}"
            
            embed.add_field(
                name=campaign.name,
                value=value,
                inline=True
            )
            
        await ctx.send(embed=embed)


async def setup(bot):
    await bot.add_cog(CampaignCog(bot))
