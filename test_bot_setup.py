#!/usr/bin/env python3
"""
Test script to verify bot setup without connecting to Discord
"""

import os
import sys

def test_imports():
    """Test that all required modules can be imported"""
    print("🧪 Testing imports...")
    
    try:
        # Test basic imports
        import django
        print("✅ Django imported")
        
        from dotenv import load_dotenv
        print("✅ python-dotenv imported")
        
        import discord
        print("✅ discord.py imported")
        
        # Add project root to path
        project_root = os.path.dirname(os.path.abspath(__file__))
        sys.path.insert(0, project_root)
        
        # Setup Django
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'daggerfall_web.settings')
        django.setup()
        print("✅ Django setup complete")
        
        # Test Django models
        from core.models import DiscordUser, Guild
        from campaigns.models import Campaign
        from characters.models import Character
        print("✅ Django models imported")
        
        # Test bot cogs
        from bot.cogs import dice, characters, campaigns, source_material, gm_tools
        print("✅ Bot cogs imported")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Setup error: {e}")
        return False

def test_environment():
    """Test environment configuration"""
    print("\n🔧 Testing environment...")
    
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    # Check for Discord token
    token = os.getenv('DISCORD_TOKEN')
    if token and token != 'your_discord_bot_token_here':
        print("✅ Discord token found")
        return True
    else:
        print("⚠️  Discord token not set or using placeholder")
        print("   Update your .env file with: DISCORD_TOKEN=your_actual_token")
        return False

def test_database():
    """Test database connection"""
    print("\n🗄️ Testing database...")
    
    try:
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
        
        if result:
            print("✅ Database connection successful")
            return True
        else:
            print("❌ Database connection failed")
            return False
            
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Testing Daggerheart Bot Setup\n")
    
    tests = [
        ("Imports", test_imports),
        ("Environment", test_environment),
        ("Database", test_database)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} test failed: {e}")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 Bot setup is ready!")
        print("\n📋 To run the bot:")
        print("1. Make sure your .env file has your Discord token")
        print("2. Run: python bot/bot.py")
    elif passed >= 2:
        print("\n⚠️  Bot setup mostly ready!")
        print("   Just need to add your Discord token to .env file")
    else:
        print("\n❌ Bot setup needs attention. Check the errors above.")
    
    return passed >= 2  # Consider it successful if imports and database work

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
