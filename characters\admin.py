from django.contrib import admin
from .models import Character, CharacterAbility, CharacterEquipment, CharacterSpell, CharacterNote


class CharacterAbilityInline(admin.TabularInline):
    model = CharacterAbility
    extra = 0


class CharacterEquipmentInline(admin.TabularInline):
    model = CharacterEquipment
    extra = 0


class CharacterSpellInline(admin.TabularInline):
    model = CharacterSpell
    extra = 0


@admin.register(Character)
class CharacterAdmin(admin.ModelAdmin):
    list_display = ['name', 'player', 'campaign', 'ancestry', 'class_name', 'level', 'is_active']
    list_filter = ['ancestry', 'class_name', 'level', 'is_active', 'created_at']
    search_fields = ['name', 'player__discord_username', 'campaign__name']
    readonly_fields = ['created_at', 'updated_at']

    inlines = [CharacterAbilityInline, CharacterEquipmentInline, CharacterSpellInline]

    fieldsets = (
        ('Basic Information', {
            'fields': ('player', 'campaign', 'name', 'ancestry', 'community', 'class_name', 'subclass', 'level')
        }),
        ('Stats', {
            'fields': ('agility', 'strength', 'finesse', 'instinct', 'presence', 'knowledge')
        }),
        ('Health & Resources', {
            'fields': ('hit_points', 'current_hp', 'stress', 'hope', 'fear')
        }),
        ('Thresholds', {
            'fields': ('minor_threshold', 'major_threshold', 'severe_threshold')
        }),
        ('Character Details', {
            'fields': ('background', 'personality', 'connections')
        }),
        ('Status', {
            'fields': ('is_active', 'created_at', 'updated_at')
        }),
    )


@admin.register(CharacterNote)
class CharacterNoteAdmin(admin.ModelAdmin):
    list_display = ['character', 'title', 'note_type', 'created_at']
    list_filter = ['note_type', 'created_at']
    search_fields = ['character__name', 'title', 'content']
