import discord
from discord.ext import commands
from source_material.models import ContentSection, Spell, Equipment, Rule, CharacterClass, Ancestry


class SourceMaterialCog(commands.Cog):
    """Source material lookup commands"""
    
    def __init__(self, bot):
        self.bot = bot

    @commands.command(name='lookup', aliases=['search'])
    async def lookup_content(self, ctx, *, search_term: str):
        """Search source material for rules, spells, etc."""
        if len(search_term) < 3:
            await ctx.send("❌ Search term must be at least 3 characters long!")
            return
            
        # Search across different content types
        results = []
        
        # Search rules
        rules = Rule.objects.filter(
            title__icontains=search_term
        )[:3]
        for rule in rules:
            results.append(('Rule', rule.title, rule.content[:200] + "..." if len(rule.content) > 200 else rule.content))
            
        # Search spells
        spells = Spell.objects.filter(
            name__icontains=search_term
        )[:3]
        for spell in spells:
            results.append(('Spell', spell.name, f"Level {spell.level} {spell.domain} spell"))
            
        # Search equipment
        equipment = Equipment.objects.filter(
            name__icontains=search_term
        )[:3]
        for item in equipment:
            results.append(('Equipment', item.name, item.description[:200] + "..." if len(item.description) > 200 else item.description))
            
        # Search content sections
        sections = ContentSection.objects.filter(
            title__icontains=search_term
        )[:2]
        for section in sections:
            results.append(('Content', section.title, section.content[:200] + "..." if len(section.content) > 200 else section.content))
            
        if not results:
            embed = discord.Embed(
                title="❌ No Results Found",
                description=f"No source material found for '{search_term}'",
                color=0xFF0000
            )
            await ctx.send(embed=embed)
            return
            
        embed = discord.Embed(
            title=f"📚 Search Results for '{search_term}'",
            color=0x8B4513
        )
        
        for content_type, title, description in results[:8]:  # Limit to 8 results
            embed.add_field(
                name=f"{content_type}: {title}",
                value=description,
                inline=False
            )
            
        if len(results) > 8:
            embed.set_footer(text=f"Showing first 8 of {len(results)} results")
            
        await ctx.send(embed=embed)

    @commands.command(name='spell')
    async def spell_lookup(self, ctx, *, spell_name: str):
        """Look up a specific spell"""
        try:
            spell = Spell.objects.get(name__iexact=spell_name)
        except Spell.DoesNotExist:
            # Try partial match
            spells = Spell.objects.filter(name__icontains=spell_name)
            if not spells.exists():
                await ctx.send(f"❌ Spell '{spell_name}' not found!")
                return
            elif spells.count() > 1:
                spell_list = "\n".join([f"• {s.name}" for s in spells[:10]])
                embed = discord.Embed(
                    title="🔮 Multiple Spells Found",
                    description=f"Did you mean one of these?\n{spell_list}",
                    color=0x8B4513
                )
                await ctx.send(embed=embed)
                return
            else:
                spell = spells.first()
                
        embed = discord.Embed(
            title=f"🔮 {spell.name}",
            description=spell.description,
            color=0x9932CC
        )
        
        embed.add_field(name="Level", value=str(spell.level), inline=True)
        embed.add_field(name="Domain", value=spell.domain, inline=True)
        embed.add_field(name="Casting Time", value=spell.casting_time, inline=True)
        embed.add_field(name="Range", value=spell.range_distance, inline=True)
        embed.add_field(name="Duration", value=spell.duration, inline=True)
        
        if spell.components:
            embed.add_field(name="Components", value=spell.components, inline=True)
            
        await ctx.send(embed=embed)

    @commands.command(name='class')
    async def class_lookup(self, ctx, *, class_name: str):
        """Look up a character class"""
        try:
            char_class = CharacterClass.objects.get(name__iexact=class_name)
        except CharacterClass.DoesNotExist:
            # Try partial match
            classes = CharacterClass.objects.filter(name__icontains=class_name)
            if not classes.exists():
                await ctx.send(f"❌ Class '{class_name}' not found!")
                return
            elif classes.count() > 1:
                class_list = "\n".join([f"• {c.name}" for c in classes])
                embed = discord.Embed(
                    title="⚔️ Multiple Classes Found",
                    description=f"Did you mean one of these?\n{class_list}",
                    color=0x8B4513
                )
                await ctx.send(embed=embed)
                return
            else:
                char_class = classes.first()
                
        embed = discord.Embed(
            title=f"⚔️ {char_class.name}",
            description=char_class.description,
            color=0x8B4513
        )
        
        embed.add_field(name="Primary Stats", value=char_class.primary_stats, inline=True)
        embed.add_field(name="Base HP", value=str(char_class.hit_points_base), inline=True)
        embed.add_field(name="HP per Level", value=str(char_class.hit_points_per_level), inline=True)
        
        # Show subclasses if any
        subclasses = char_class.subclasses.all()
        if subclasses.exists():
            subclass_list = "\n".join([f"• {s.name}" for s in subclasses])
            embed.add_field(name="Subclasses", value=subclass_list, inline=False)
            
        await ctx.send(embed=embed)

    @commands.command(name='ancestry')
    async def ancestry_lookup(self, ctx, *, ancestry_name: str):
        """Look up an ancestry"""
        try:
            ancestry = Ancestry.objects.get(name__iexact=ancestry_name)
        except Ancestry.DoesNotExist:
            # Try partial match
            ancestries = Ancestry.objects.filter(name__icontains=ancestry_name)
            if not ancestries.exists():
                await ctx.send(f"❌ Ancestry '{ancestry_name}' not found!")
                return
            elif ancestries.count() > 1:
                ancestry_list = "\n".join([f"• {a.name}" for a in ancestries])
                embed = discord.Embed(
                    title="🧬 Multiple Ancestries Found",
                    description=f"Did you mean one of these?\n{ancestry_list}",
                    color=0x8B4513
                )
                await ctx.send(embed=embed)
                return
            else:
                ancestry = ancestries.first()
                
        embed = discord.Embed(
            title=f"🧬 {ancestry.name}",
            description=ancestry.description,
            color=0x8B4513
        )
        
        embed.add_field(name="Size", value=ancestry.size, inline=True)
        embed.add_field(name="Speed", value=f"{ancestry.speed} feet", inline=True)
        
        if ancestry.special_abilities:
            embed.add_field(name="Special Abilities", value=ancestry.special_abilities, inline=False)
            
        await ctx.send(embed=embed)

    @commands.command(name='item', aliases=['equipment'])
    async def item_lookup(self, ctx, *, item_name: str):
        """Look up equipment or items"""
        try:
            item = Equipment.objects.get(name__iexact=item_name)
        except Equipment.DoesNotExist:
            # Try partial match
            items = Equipment.objects.filter(name__icontains=item_name)
            if not items.exists():
                await ctx.send(f"❌ Item '{item_name}' not found!")
                return
            elif items.count() > 1:
                item_list = "\n".join([f"• {i.name}" for i in items[:10]])
                embed = discord.Embed(
                    title="🎒 Multiple Items Found",
                    description=f"Did you mean one of these?\n{item_list}",
                    color=0x8B4513
                )
                await ctx.send(embed=embed)
                return
            else:
                item = items.first()
                
        embed = discord.Embed(
            title=f"🎒 {item.name}",
            description=item.description,
            color=0x8B4513
        )
        
        embed.add_field(name="Type", value=item.item_type.title(), inline=True)
        
        if item.cost:
            embed.add_field(name="Cost", value=item.cost, inline=True)
            
        if item.weight:
            embed.add_field(name="Weight", value=item.weight, inline=True)
            
        # Weapon properties
        if item.damage_die:
            embed.add_field(name="Damage", value=item.damage_die, inline=True)
            
        if item.weapon_tags:
            embed.add_field(name="Tags", value=item.weapon_tags, inline=True)
            
        # Armor properties
        if item.armor_value > 0:
            embed.add_field(name="Armor Value", value=str(item.armor_value), inline=True)
            
        await ctx.send(embed=embed)

    @commands.command(name='rule')
    async def rule_lookup(self, ctx, *, rule_name: str):
        """Look up a specific rule"""
        try:
            rule = Rule.objects.get(title__iexact=rule_name)
        except Rule.DoesNotExist:
            # Try partial match
            rules = Rule.objects.filter(title__icontains=rule_name)
            if not rules.exists():
                await ctx.send(f"❌ Rule '{rule_name}' not found!")
                return
            elif rules.count() > 1:
                rule_list = "\n".join([f"• {r.title}" for r in rules[:10]])
                embed = discord.Embed(
                    title="📖 Multiple Rules Found",
                    description=f"Did you mean one of these?\n{rule_list}",
                    color=0x8B4513
                )
                await ctx.send(embed=embed)
                return
            else:
                rule = rules.first()
                
        embed = discord.Embed(
            title=f"📖 {rule.title}",
            description=rule.content,
            color=0x8B4513
        )
        
        embed.add_field(name="Category", value=rule.category.title(), inline=True)
        
        if rule.tags:
            embed.add_field(name="Tags", value=rule.tags, inline=True)
            
        await ctx.send(embed=embed)


async def setup(bot):
    await bot.add_cog(SourceMaterialCog(bot))
