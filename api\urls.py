from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer
from . import views

# Create a router and register our viewsets with it
router = DefaultRouter()
router.register(r'users', views.DiscordUserViewSet)
router.register(r'guilds', views.GuildViewSet)
router.register(r'dice-rolls', views.DiceRollViewSet)
router.register(r'campaigns', views.CampaignViewSet)
router.register(r'characters', views.CharacterViewSet)
router.register(r'spells', views.SpellViewSet)
router.register(r'equipment', views.EquipmentViewSet)
router.register(r'rules', views.RuleViewSet)
router.register(r'classes', views.CharacterClassViewSet)
router.register(r'ancestries', views.AncestryViewSet)

# The API URLs are now determined automatically by the router
urlpatterns = [
    path('', include(router.urls)),
    path('auth/', include('rest_framework.urls')),  # Login/logout views for browsable API
]
