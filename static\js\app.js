// Daggerheart Master JavaScript

// API Base URL
const API_BASE_URL = '/api/';

// Utility Functions
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// CSRF Token for Django
const csrftoken = getCookie('csrftoken');

// API Helper Functions
async function apiRequest(endpoint, options = {}) {
    const url = API_BASE_URL + endpoint;
    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrftoken,
        },
        credentials: 'same-origin',
    };
    
    const finalOptions = { ...defaultOptions, ...options };
    
    try {
        const response = await fetch(url, finalOptions);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return await response.json();
    } catch (error) {
        console.error('API request failed:', error);
        throw error;
    }
}

// Dice Rolling Functions
class DiceRoller {
    static parseDiceExpression(expression) {
        // Parse dice notation like "2d12+3" or "d20"
        const regex = /^(\d*)d(\d+)([+-]\d+)?$/i;
        const match = expression.trim().match(regex);
        
        if (!match) return null;
        
        const numDice = parseInt(match[1]) || 1;
        const dieSize = parseInt(match[2]);
        const modifier = parseInt(match[3]) || 0;
        
        if (numDice > 20 || dieSize > 100 || numDice < 1 || dieSize < 2) {
            return null;
        }
        
        return { numDice, dieSize, modifier };
    }
    
    static rollDice(numDice, dieSize, modifier = 0) {
        const rolls = [];
        for (let i = 0; i < numDice; i++) {
            rolls.push(Math.floor(Math.random() * dieSize) + 1);
        }
        const total = rolls.reduce((sum, roll) => sum + roll, 0) + modifier;
        return { rolls, total, modifier };
    }
    
    static formatResult(expression, rolls, total, modifier) {
        let breakdown = '';
        if (rolls.length > 1) {
            breakdown = `(${rolls.join(' + ')}`;
            if (modifier !== 0) {
                breakdown += ` ${modifier >= 0 ? '+' : ''}${modifier}`;
            }
            breakdown += `) = ${total}`;
        } else if (modifier !== 0) {
            breakdown = `(${rolls[0]} ${modifier >= 0 ? '+' : ''}${modifier}) = ${total}`;
        }
        
        return {
            expression,
            total,
            breakdown,
            rolls
        };
    }
}

// Character Management
class CharacterManager {
    static async loadCharacters() {
        try {
            const characters = await apiRequest('characters/');
            return characters.results || characters;
        } catch (error) {
            console.error('Failed to load characters:', error);
            return [];
        }
    }
    
    static async createCharacter(characterData) {
        try {
            return await apiRequest('characters/', {
                method: 'POST',
                body: JSON.stringify(characterData)
            });
        } catch (error) {
            console.error('Failed to create character:', error);
            throw error;
        }
    }
    
    static async updateCharacter(characterId, characterData) {
        try {
            return await apiRequest(`characters/${characterId}/`, {
                method: 'PATCH',
                body: JSON.stringify(characterData)
            });
        } catch (error) {
            console.error('Failed to update character:', error);
            throw error;
        }
    }
}

// Campaign Management
class CampaignManager {
    static async loadCampaigns() {
        try {
            const campaigns = await apiRequest('campaigns/');
            return campaigns.results || campaigns;
        } catch (error) {
            console.error('Failed to load campaigns:', error);
            return [];
        }
    }
    
    static async loadCampaignDetails(campaignId) {
        try {
            return await apiRequest(`campaigns/${campaignId}/`);
        } catch (error) {
            console.error('Failed to load campaign details:', error);
            throw error;
        }
    }
}

// Source Material Search
class SourceMaterialManager {
    static async searchSpells(query) {
        try {
            return await apiRequest(`spells/search/?q=${encodeURIComponent(query)}`);
        } catch (error) {
            console.error('Failed to search spells:', error);
            return [];
        }
    }
    
    static async searchEquipment(query) {
        try {
            return await apiRequest(`equipment/search/?q=${encodeURIComponent(query)}`);
        } catch (error) {
            console.error('Failed to search equipment:', error);
            return [];
        }
    }
    
    static async searchRules(query) {
        try {
            return await apiRequest(`rules/search/?q=${encodeURIComponent(query)}`);
        } catch (error) {
            console.error('Failed to search rules:', error);
            return [];
        }
    }
}

// UI Helper Functions
function showLoading(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        element.innerHTML = `
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
        `;
    }
}

function showError(elementId, message) {
    const element = document.getElementById(elementId);
    if (element) {
        element.innerHTML = `
            <div class="alert alert-danger" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                ${message}
            </div>
        `;
    }
}

function showSuccess(elementId, message) {
    const element = document.getElementById(elementId);
    if (element) {
        element.innerHTML = `
            <div class="alert alert-success" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                ${message}
            </div>
        `;
    }
}

// Toast Notifications
function showToast(message, type = 'info') {
    const toastContainer = document.getElementById('toast-container') || createToastContainer();
    
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;
    
    toastContainer.appendChild(toast);
    
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    
    // Remove toast element after it's hidden
    toast.addEventListener('hidden.bs.toast', () => {
        toast.remove();
    });
}

function createToastContainer() {
    const container = document.createElement('div');
    container.id = 'toast-container';
    container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
    container.style.zIndex = '1055';
    document.body.appendChild(container);
    return container;
}

// Initialize App
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Initialize popovers
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    console.log('Daggerheart Master app initialized');
});

// Export for global use
window.DaggerheartMaster = {
    DiceRoller,
    CharacterManager,
    CampaignManager,
    SourceMaterialManager,
    showLoading,
    showError,
    showSuccess,
    showToast,
    apiRequest
};
