from rest_framework import serializers
from core.models import Discord<PERSON><PERSON>, <PERSON>, <PERSON>ceRoll
from campaigns.models import Campaign, Session, NPC, Location, CampaignMembership
from characters.models import Character, CharacterAbility, CharacterEquipment, CharacterSpell, CharacterNote
from source_material.models import Spell, Equipment, Rule, CharacterClass, Ancestry, Subclass


class DiscordUserSerializer(serializers.ModelSerializer):
    """Serializer for Discord users"""
    class Meta:
        model = DiscordUser
        fields = ['id', 'discord_id', 'discord_username', 'discord_discriminator', 
                 'avatar_url', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']


class GuildSerializer(serializers.ModelSerializer):
    """Serializer for Discord guilds"""
    owner_username = serializers.CharField(source='owner.discord_username', read_only=True)
    member_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Guild
        fields = ['id', 'guild_id', 'name', 'icon_url', 'owner_username', 
                 'member_count', 'bot_enabled', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_member_count(self, obj):
        return obj.members.count()


class DiceRollSerializer(serializers.ModelSerializer):
    """Serializer for dice rolls"""
    user_username = serializers.CharField(source='user.discord_username', read_only=True)
    guild_name = serializers.CharField(source='guild.name', read_only=True)
    
    class Meta:
        model = DiceRoll
        fields = ['id', 'user_username', 'guild_name', 'roll_type', 'dice_expression',
                 'result', 'individual_rolls', 'modifier', 'description', 'timestamp']
        read_only_fields = ['id', 'timestamp']


class CampaignMembershipSerializer(serializers.ModelSerializer):
    """Serializer for campaign membership"""
    player_username = serializers.CharField(source='player.discord_username', read_only=True)
    
    class Meta:
        model = CampaignMembership
        fields = ['player_username', 'joined_at', 'is_active']


class CampaignSerializer(serializers.ModelSerializer):
    """Serializer for campaigns"""
    gm_username = serializers.CharField(source='game_master.discord_username', read_only=True)
    guild_name = serializers.CharField(source='guild.name', read_only=True)
    player_count = serializers.SerializerMethodField()
    players = CampaignMembershipSerializer(source='campaignmembership_set', many=True, read_only=True)
    
    class Meta:
        model = Campaign
        fields = ['id', 'name', 'description', 'guild_name', 'gm_username', 
                 'player_count', 'players', 'is_active', 'max_players',
                 'session_channel_id', 'notes_channel_id', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_player_count(self, obj):
        return obj.players.count()


class SessionSerializer(serializers.ModelSerializer):
    """Serializer for game sessions"""
    campaign_name = serializers.CharField(source='campaign.name', read_only=True)
    
    class Meta:
        model = Session
        fields = ['id', 'campaign_name', 'session_number', 'title', 'description',
                 'scheduled_date', 'actual_start', 'actual_end', 'notes', 'status']
        read_only_fields = ['id']


class LocationSerializer(serializers.ModelSerializer):
    """Serializer for campaign locations"""
    campaign_name = serializers.CharField(source='campaign.name', read_only=True)
    parent_location_name = serializers.CharField(source='parent_location.name', read_only=True)
    
    class Meta:
        model = Location
        fields = ['id', 'campaign_name', 'name', 'description', 'location_type',
                 'parent_location_name', 'notes', 'created_at']
        read_only_fields = ['id', 'created_at']


class NPCSerializer(serializers.ModelSerializer):
    """Serializer for NPCs"""
    campaign_name = serializers.CharField(source='campaign.name', read_only=True)
    location_name = serializers.CharField(source='location.name', read_only=True)
    
    class Meta:
        model = NPC
        fields = ['id', 'campaign_name', 'name', 'description', 'ancestry', 'class_name',
                 'level', 'agility', 'strength', 'finesse', 'instinct', 'presence', 'knowledge',
                 'hit_points', 'current_hp', 'location_name', 'notes', 'is_alive', 'created_at']
        read_only_fields = ['id', 'created_at']


class CharacterAbilitySerializer(serializers.ModelSerializer):
    """Serializer for character abilities"""
    class Meta:
        model = CharacterAbility
        fields = ['id', 'name', 'description', 'ability_type', 'source', 'is_active']


class CharacterEquipmentSerializer(serializers.ModelSerializer):
    """Serializer for character equipment"""
    class Meta:
        model = CharacterEquipment
        fields = ['id', 'name', 'description', 'item_type', 'quantity', 'is_equipped',
                 'damage_die', 'weapon_tags', 'armor_value']


class CharacterSpellSerializer(serializers.ModelSerializer):
    """Serializer for character spells"""
    class Meta:
        model = CharacterSpell
        fields = ['id', 'name', 'description', 'spell_level', 'domain', 'casting_time',
                 'range_distance', 'duration', 'components']


class CharacterNoteSerializer(serializers.ModelSerializer):
    """Serializer for character notes"""
    class Meta:
        model = CharacterNote
        fields = ['id', 'title', 'content', 'note_type', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']


class CharacterSerializer(serializers.ModelSerializer):
    """Serializer for characters"""
    player_username = serializers.CharField(source='player.discord_username', read_only=True)
    campaign_name = serializers.CharField(source='campaign.name', read_only=True)
    abilities = CharacterAbilitySerializer(many=True, read_only=True)
    equipment = CharacterEquipmentSerializer(many=True, read_only=True)
    spells = CharacterSpellSerializer(many=True, read_only=True)
    notes = CharacterNoteSerializer(many=True, read_only=True)
    
    class Meta:
        model = Character
        fields = ['id', 'player_username', 'campaign_name', 'name', 'ancestry', 'community',
                 'class_name', 'subclass', 'level', 'agility', 'strength', 'finesse', 'instinct',
                 'presence', 'knowledge', 'hit_points', 'current_hp', 'stress', 'hope', 'fear',
                 'minor_threshold', 'major_threshold', 'severe_threshold', 'background',
                 'personality', 'connections', 'is_active', 'abilities', 'equipment', 'spells',
                 'notes', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']


class SpellSerializer(serializers.ModelSerializer):
    """Serializer for spells"""
    class Meta:
        model = Spell
        fields = ['id', 'name', 'description', 'level', 'domain', 'casting_time',
                 'range_distance', 'duration', 'components']


class EquipmentSerializer(serializers.ModelSerializer):
    """Serializer for equipment"""
    class Meta:
        model = Equipment
        fields = ['id', 'name', 'description', 'item_type', 'cost', 'weight',
                 'damage_die', 'weapon_tags', 'armor_value']


class RuleSerializer(serializers.ModelSerializer):
    """Serializer for rules"""
    class Meta:
        model = Rule
        fields = ['id', 'title', 'content', 'category', 'tags']


class SubclassSerializer(serializers.ModelSerializer):
    """Serializer for subclasses"""
    class Meta:
        model = Subclass
        fields = ['id', 'name', 'description']


class CharacterClassSerializer(serializers.ModelSerializer):
    """Serializer for character classes"""
    subclasses = SubclassSerializer(many=True, read_only=True)
    
    class Meta:
        model = CharacterClass
        fields = ['id', 'name', 'description', 'primary_stats', 'hit_points_base',
                 'hit_points_per_level', 'subclasses']


class AncestrySerializer(serializers.ModelSerializer):
    """Serializer for ancestries"""
    class Meta:
        model = Ancestry
        fields = ['id', 'name', 'description', 'size', 'speed', 'special_abilities']
