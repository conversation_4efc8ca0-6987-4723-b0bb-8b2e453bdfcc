from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404

from core.models import DiscordUser, Guild, DiceRoll
from campaigns.models import Campaign, Session, NPC, Location
from characters.models import Character
from source_material.models import Spell, Equipment, Rule, CharacterClass, Ancestry

from .serializers import (
    DiscordUserSerializer, GuildSerializer, DiceRollSerializer,
    CampaignSerializer, SessionSerializer, NPCSerializer, LocationSerializer,
    CharacterSerializer, SpellSerializer, EquipmentSerializer, RuleSerializer,
    CharacterClassSerializer, AncestrySerializer
)


class DiscordUserViewSet(viewsets.ModelViewSet):
    """API endpoints for Discord users"""
    queryset = DiscordUser.objects.all()
    serializer_class = DiscordUserSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        # Users can only see their own data
        return DiscordUser.objects.filter(user=self.request.user)


class GuildViewSet(viewsets.ReadOnlyModelViewSet):
    """API endpoints for Discord guilds"""
    queryset = Guild.objects.all()
    serializer_class = GuildSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        # Users can only see guilds they're members of
        try:
            discord_user = DiscordUser.objects.get(user=self.request.user)
            return Guild.objects.filter(members=discord_user)
        except DiscordUser.DoesNotExist:
            return Guild.objects.none()


class DiceRollViewSet(viewsets.ReadOnlyModelViewSet):
    """API endpoints for dice rolls"""
    queryset = DiceRoll.objects.all()
    serializer_class = DiceRollSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        # Users can only see their own rolls
        try:
            discord_user = DiscordUser.objects.get(user=self.request.user)
            return DiceRoll.objects.filter(user=discord_user)
        except DiscordUser.DoesNotExist:
            return DiceRoll.objects.none()


class CampaignViewSet(viewsets.ModelViewSet):
    """API endpoints for campaigns"""
    queryset = Campaign.objects.all()
    serializer_class = CampaignSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        # Users can see campaigns they're GM or player in
        try:
            discord_user = DiscordUser.objects.get(user=self.request.user)
            return Campaign.objects.filter(
                models.Q(game_master=discord_user) | models.Q(players=discord_user)
            ).distinct()
        except DiscordUser.DoesNotExist:
            return Campaign.objects.none()

    @action(detail=True, methods=['get'])
    def sessions(self, request, pk=None):
        """Get sessions for a campaign"""
        campaign = self.get_object()
        sessions = Session.objects.filter(campaign=campaign)
        serializer = SessionSerializer(sessions, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def npcs(self, request, pk=None):
        """Get NPCs for a campaign"""
        campaign = self.get_object()
        npcs = NPC.objects.filter(campaign=campaign)
        serializer = NPCSerializer(npcs, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def locations(self, request, pk=None):
        """Get locations for a campaign"""
        campaign = self.get_object()
        locations = Location.objects.filter(campaign=campaign)
        serializer = LocationSerializer(locations, many=True)
        return Response(serializer.data)


class CharacterViewSet(viewsets.ModelViewSet):
    """API endpoints for characters"""
    queryset = Character.objects.all()
    serializer_class = CharacterSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        # Users can see their own characters and characters in campaigns they GM
        try:
            discord_user = DiscordUser.objects.get(user=self.request.user)
            return Character.objects.filter(
                models.Q(player=discord_user) |
                models.Q(campaign__game_master=discord_user)
            ).distinct()
        except DiscordUser.DoesNotExist:
            return Character.objects.none()


class SpellViewSet(viewsets.ReadOnlyModelViewSet):
    """API endpoints for spells"""
    queryset = Spell.objects.all()
    serializer_class = SpellSerializer
    permission_classes = [IsAuthenticated]

    @action(detail=False, methods=['get'])
    def search(self, request):
        """Search spells by name or description"""
        query = request.query_params.get('q', '')
        if query:
            spells = Spell.objects.filter(
                models.Q(name__icontains=query) |
                models.Q(description__icontains=query)
            )
        else:
            spells = Spell.objects.all()

        serializer = self.get_serializer(spells, many=True)
        return Response(serializer.data)


class EquipmentViewSet(viewsets.ReadOnlyModelViewSet):
    """API endpoints for equipment"""
    queryset = Equipment.objects.all()
    serializer_class = EquipmentSerializer
    permission_classes = [IsAuthenticated]

    @action(detail=False, methods=['get'])
    def search(self, request):
        """Search equipment by name or description"""
        query = request.query_params.get('q', '')
        if query:
            equipment = Equipment.objects.filter(
                models.Q(name__icontains=query) |
                models.Q(description__icontains=query)
            )
        else:
            equipment = Equipment.objects.all()

        serializer = self.get_serializer(equipment, many=True)
        return Response(serializer.data)


class RuleViewSet(viewsets.ReadOnlyModelViewSet):
    """API endpoints for rules"""
    queryset = Rule.objects.all()
    serializer_class = RuleSerializer
    permission_classes = [IsAuthenticated]

    @action(detail=False, methods=['get'])
    def search(self, request):
        """Search rules by title or content"""
        query = request.query_params.get('q', '')
        if query:
            rules = Rule.objects.filter(
                models.Q(title__icontains=query) |
                models.Q(content__icontains=query)
            )
        else:
            rules = Rule.objects.all()

        serializer = self.get_serializer(rules, many=True)
        return Response(serializer.data)


class CharacterClassViewSet(viewsets.ReadOnlyModelViewSet):
    """API endpoints for character classes"""
    queryset = CharacterClass.objects.all()
    serializer_class = CharacterClassSerializer
    permission_classes = [IsAuthenticated]


class AncestryViewSet(viewsets.ReadOnlyModelViewSet):
    """API endpoints for ancestries"""
    queryset = Ancestry.objects.all()
    serializer_class = AncestrySerializer
    permission_classes = [IsAuthenticated]
