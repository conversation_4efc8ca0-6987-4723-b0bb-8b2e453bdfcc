from django.contrib import admin
from .models import Campaign, CampaignMembership, Session, Location, NPC


@admin.register(Campaign)
class CampaignAdmin(admin.ModelAdmin):
    list_display = ['name', 'game_master', 'guild', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'game_master__discord_username']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(CampaignMembership)
class CampaignMembershipAdmin(admin.ModelAdmin):
    list_display = ['campaign', 'player', 'joined_at', 'is_active']
    list_filter = ['is_active', 'joined_at']
    search_fields = ['campaign__name', 'player__discord_username']


@admin.register(Session)
class SessionAdmin(admin.ModelAdmin):
    list_display = ['campaign', 'session_number', 'title', 'status', 'scheduled_date']
    list_filter = ['status', 'scheduled_date']
    search_fields = ['campaign__name', 'title']


@admin.register(Location)
class LocationAdmin(admin.ModelAdmin):
    list_display = ['name', 'campaign', 'location_type', 'created_at']
    list_filter = ['location_type', 'created_at']
    search_fields = ['name', 'campaign__name']


@admin.register(NPC)
class NPCAdmin(admin.ModelAdmin):
    list_display = ['name', 'campaign', 'ancestry', 'class_name', 'level', 'is_alive']
    list_filter = ['ancestry', 'class_name', 'level', 'is_alive']
    search_fields = ['name', 'campaign__name']
