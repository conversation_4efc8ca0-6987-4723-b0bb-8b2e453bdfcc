from django.db import models
from django.core.files.storage import default_storage


class SourceDocument(models.Model):
    """Model for storing source material PDFs and documents"""
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    file_path = models.CharField(max_length=500)  # Path to the PDF file
    file_size = models.BigIntegerField()
    upload_date = models.DateTimeField(auto_now_add=True)
    last_processed = models.DateTimeField(null=True, blank=True)
    is_processed = models.BooleanField(default=False)

    DOCUMENT_TYPES = [
        ('core_rules', 'Core Rules'),
        ('character_sheets', 'Character Sheets'),
        ('additional_content', 'Additional Content'),
        ('printer_friendly', 'Printer Friendly'),
        ('homebrew', 'Homebrew Content'),
    ]
    document_type = models.CharField(max_length=50, choices=DOCUMENT_TYPES)

    def __str__(self):
        return self.title


class ContentSection(models.Model):
    """Extracted sections from source documents"""
    document = models.ForeignKey(SourceDocument, on_delete=models.CASCADE, related_name='sections')
    title = models.CharField(max_length=300)
    content = models.TextField()
    page_number = models.IntegerField()
    section_type = models.CharField(max_length=100)  # chapter, rule, spell, class, etc.
    parent_section = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True)

    # For search and indexing
    keywords = models.TextField(blank=True)  # Comma-separated keywords

    def __str__(self):
        return f"{self.document.title} - {self.title}"

    class Meta:
        ordering = ['document', 'page_number']


class Ancestry(models.Model):
    """Daggerheart ancestries"""
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField()
    source_section = models.ForeignKey(ContentSection, on_delete=models.SET_NULL, null=True, blank=True)

    # Mechanical benefits
    size = models.CharField(max_length=50, default='Medium')
    speed = models.IntegerField(default=25)
    special_abilities = models.TextField(blank=True)

    def __str__(self):
        return self.name


class CharacterClass(models.Model):
    """Daggerheart character classes"""
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField()
    source_section = models.ForeignKey(ContentSection, on_delete=models.SET_NULL, null=True, blank=True)

    # Class mechanics
    primary_stats = models.CharField(max_length=200)  # Comma-separated stat names
    hit_points_base = models.IntegerField(default=10)
    hit_points_per_level = models.IntegerField(default=5)

    def __str__(self):
        return self.name


class Subclass(models.Model):
    """Character subclasses/specializations"""
    name = models.CharField(max_length=100)
    character_class = models.ForeignKey(CharacterClass, on_delete=models.CASCADE, related_name='subclasses')
    description = models.TextField()
    source_section = models.ForeignKey(ContentSection, on_delete=models.SET_NULL, null=True, blank=True)

    def __str__(self):
        return f"{self.character_class.name} - {self.name}"

    class Meta:
        unique_together = ('name', 'character_class')


class Spell(models.Model):
    """Spell database"""
    name = models.CharField(max_length=200, unique=True)
    description = models.TextField()
    level = models.IntegerField()
    domain = models.CharField(max_length=100)
    casting_time = models.CharField(max_length=100)
    range_distance = models.CharField(max_length=100)
    duration = models.CharField(max_length=100)
    components = models.CharField(max_length=200, blank=True)
    source_section = models.ForeignKey(ContentSection, on_delete=models.SET_NULL, null=True, blank=True)

    def __str__(self):
        return f"{self.name} (Level {self.level})"

    class Meta:
        ordering = ['level', 'name']


class Equipment(models.Model):
    """Equipment and items database"""
    name = models.CharField(max_length=200, unique=True)
    description = models.TextField()
    item_type = models.CharField(max_length=50)
    cost = models.CharField(max_length=100, blank=True)
    weight = models.CharField(max_length=50, blank=True)

    # Weapon properties
    damage_die = models.CharField(max_length=20, blank=True)
    weapon_tags = models.CharField(max_length=200, blank=True)

    # Armor properties
    armor_value = models.IntegerField(default=0)

    source_section = models.ForeignKey(ContentSection, on_delete=models.SET_NULL, null=True, blank=True)

    def __str__(self):
        return self.name


class Rule(models.Model):
    """Game rules and mechanics"""
    title = models.CharField(max_length=200)
    content = models.TextField()
    category = models.CharField(max_length=100)  # combat, magic, social, etc.
    tags = models.CharField(max_length=300, blank=True)  # Comma-separated tags
    source_section = models.ForeignKey(ContentSection, on_delete=models.SET_NULL, null=True, blank=True)

    def __str__(self):
        return self.title

    class Meta:
        ordering = ['category', 'title']
