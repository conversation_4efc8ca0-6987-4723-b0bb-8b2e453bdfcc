from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone


class DiscordUser(models.Model):
    """Extended user model for Discord integration"""
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    discord_id = models.CharField(max_length=20, unique=True)
    discord_username = models.CharField(max_length=100)
    discord_discriminator = models.CharField(max_length=4, blank=True)
    avatar_url = models.URLField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.discord_username}#{self.discord_discriminator}"


class Guild(models.Model):
    """Discord Guild/Server model"""
    guild_id = models.CharField(max_length=20, unique=True)
    name = models.CharField(max_length=100)
    icon_url = models.URLField(blank=True)
    owner = models.ForeignKey(DiscordUser, on_delete=models.CASCADE, related_name='owned_guilds')
    members = models.ManyToManyField(DiscordUser, through='GuildMembership')
    bot_enabled = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name


class GuildMembership(models.Model):
    """Through model for Guild-DiscordUser relationship"""
    guild = models.ForeignKey(Guild, on_delete=models.CASCADE)
    user = models.ForeignKey(DiscordUser, on_delete=models.CASCADE)
    is_admin = models.BooleanField(default=False)
    joined_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('guild', 'user')


class DiceRoll(models.Model):
    """Model to store dice roll history"""
    ROLL_TYPES = [
        ('action', 'Action Roll'),
        ('damage', 'Damage Roll'),
        ('fear', 'Fear Roll'),
        ('hope', 'Hope Roll'),
        ('custom', 'Custom Roll'),
    ]

    user = models.ForeignKey(DiscordUser, on_delete=models.CASCADE)
    guild = models.ForeignKey(Guild, on_delete=models.CASCADE, null=True, blank=True)
    roll_type = models.CharField(max_length=20, choices=ROLL_TYPES)
    dice_expression = models.CharField(max_length=100)  # e.g., "2d12+3"
    result = models.IntegerField()
    individual_rolls = models.JSONField()  # Store individual die results
    modifier = models.IntegerField(default=0)
    description = models.TextField(blank=True)
    timestamp = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.user} rolled {self.dice_expression} = {self.result}"

    class Meta:
        ordering = ['-timestamp']
