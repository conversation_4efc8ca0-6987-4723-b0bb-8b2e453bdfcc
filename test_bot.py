#!/usr/bin/env python3
"""
Test script for the Daggerheart Discord Bot
This script tests the bot functionality without connecting to Discord
"""

import os
import sys
import django
from django.conf import settings

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'daggerfall_web.settings')
django.setup()

from core.models import DiscordUser, Guild, DiceRoll
from campaigns.models import Campaign, CampaignMembership
from characters.models import Character
from source_material.models import Spell, Equipment, CharacterClass, Ancestry

def test_database_models():
    """Test that all database models work correctly"""
    print("🧪 Testing Database Models...")
    
    # Test creating a Django user first
    from django.contrib.auth.models import User
    django_user = User.objects.create_user(
        username="testuser",
        email="<EMAIL>",
        password="testpass123"
    )

    # Test creating a Discord user
    user = DiscordUser.objects.create(
        user=django_user,
        discord_id="123456789",
        discord_username="TestUser",
        discord_discriminator="1234",
        avatar_url="https://example.com/avatar.png"
    )
    print(f"✅ Created Discord User: {user}")
    
    # Test creating a guild
    guild = Guild.objects.create(
        guild_id="987654321",
        name="Test Guild",
        owner=user,
        bot_enabled=True
    )
    print(f"✅ Created Guild: {guild}")
    
    # Test creating a campaign
    campaign = Campaign.objects.create(
        name="Test Campaign",
        description="A test campaign for Daggerheart",
        guild=guild,
        game_master=user,
        max_players=6
    )
    print(f"✅ Created Campaign: {campaign}")
    
    # Test creating a character
    character = Character.objects.create(
        player=user,
        campaign=campaign,
        name="Test Character",
        ancestry="Human",
        class_name="Guardian",
        level=1,
        agility=1,
        strength=2,
        finesse=0,
        instinct=1,
        presence=0,
        knowledge=-1,
        hit_points=15,
        current_hp=15,
        hope=2,
        fear=0
    )
    print(f"✅ Created Character: {character}")
    
    # Test creating a dice roll
    dice_roll = DiceRoll.objects.create(
        user=user,
        guild=guild,
        roll_type="action",
        dice_expression="2d12+2",
        result=18,
        individual_rolls=[8, 8],
        modifier=2,
        description="Critical Success"
    )
    print(f"✅ Created Dice Roll: {dice_roll}")
    
    print("✅ All database models working correctly!")
    return True

def test_dice_rolling():
    """Test dice rolling functionality"""
    print("\n🎲 Testing Dice Rolling...")
    
    import random
    import re
    
    def parse_dice_expression(expression):
        """Parse dice expression like '2d12+3' or 'd20'"""
        expr = expression.replace(' ', '').lower()
        pattern = r'^(\d*)d(\d+)([+-]\d+)?$'
        match = re.match(pattern, expr)
        
        if not match:
            return None
            
        num_dice = int(match.group(1)) if match.group(1) else 1
        die_size = int(match.group(2))
        modifier = int(match.group(3)) if match.group(3) else 0
        
        return num_dice, die_size, modifier
    
    def roll_dice(num_dice, die_size, modifier=0):
        """Roll dice and return results"""
        rolls = [random.randint(1, die_size) for _ in range(num_dice)]
        total = sum(rolls) + modifier
        return rolls, total, modifier
    
    # Test various dice expressions
    test_expressions = ["2d12+3", "d20", "4d6-1", "1d8+2"]
    
    for expr in test_expressions:
        parsed = parse_dice_expression(expr)
        if parsed:
            num_dice, die_size, modifier = parsed
            rolls, total, mod = roll_dice(num_dice, die_size, modifier)
            print(f"✅ {expr}: {rolls} + {modifier} = {total}")
        else:
            print(f"❌ Failed to parse: {expr}")
    
    print("✅ Dice rolling functionality working!")
    return True

def test_source_material():
    """Test source material functionality"""
    print("\n📚 Testing Source Material...")
    
    # Create test ancestry
    ancestry = Ancestry.objects.create(
        name="Human",
        description="Versatile and adaptable people",
        size="Medium",
        speed=30,
        special_abilities="Extra skill point at character creation"
    )
    print(f"✅ Created Ancestry: {ancestry}")
    
    # Create test character class
    char_class = CharacterClass.objects.create(
        name="Guardian",
        description="Stalwart defenders who protect their allies",
        primary_stats="Strength, Presence",
        hit_points_base=10,
        hit_points_per_level=6
    )
    print(f"✅ Created Character Class: {char_class}")
    
    # Create test spell
    spell = Spell.objects.create(
        name="Healing Light",
        description="Restore hit points to a target",
        level=1,
        domain="Grace",
        casting_time="Action",
        range_distance="Close",
        duration="Instantaneous",
        components="Somatic"
    )
    print(f"✅ Created Spell: {spell}")
    
    # Create test equipment
    equipment = Equipment.objects.create(
        name="Longsword",
        description="A versatile one-handed weapon",
        item_type="weapon",
        cost="15 gold",
        weight="3 lbs",
        damage_die="d8",
        weapon_tags="Versatile"
    )
    print(f"✅ Created Equipment: {equipment}")
    
    print("✅ Source material functionality working!")
    return True

def test_api_endpoints():
    """Test that API endpoints are accessible"""
    print("\n🌐 Testing API Endpoints...")
    
    try:
        from api.views import CampaignViewSet, CharacterViewSet, SpellViewSet
        print("✅ API views imported successfully")
        
        from api.serializers import CampaignSerializer, CharacterSerializer, SpellSerializer
        print("✅ API serializers imported successfully")
        
        print("✅ API endpoints ready!")
        return True
    except ImportError as e:
        print(f"❌ API import error: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting Daggerheart Master Bot Tests\n")
    
    tests = [
        test_database_models,
        test_dice_rolling,
        test_source_material,
        test_api_endpoints
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed with error: {e}")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The Daggerheart Master Bot is ready!")
        print("\n📋 Next Steps:")
        print("1. Set up your Discord bot token in .env file")
        print("2. Invite the bot to your Discord server")
        print("3. Run: python bot/bot.py")
        print("4. Access the web interface at http://localhost:8000")
        print("5. Admin interface at http://localhost:8000/admin (admin/admin123)")
    else:
        print("❌ Some tests failed. Please check the errors above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
