# Generated by Django 5.2.3 on 2025-06-15 00:26

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='SourceDocument',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('file_path', models.CharField(max_length=500)),
                ('file_size', models.BigIntegerField()),
                ('upload_date', models.DateTimeField(auto_now_add=True)),
                ('last_processed', models.DateTimeField(blank=True, null=True)),
                ('is_processed', models.BooleanField(default=False)),
                ('document_type', models.CharField(choices=[('core_rules', 'Core Rules'), ('character_sheets', 'Character Sheets'), ('additional_content', 'Additional Content'), ('printer_friendly', 'Printer Friendly'), ('homebrew', 'Homebrew Content')], max_length=50)),
            ],
        ),
        migrations.CreateModel(
            name='ContentSection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=300)),
                ('content', models.TextField()),
                ('page_number', models.IntegerField()),
                ('section_type', models.CharField(max_length=100)),
                ('keywords', models.TextField(blank=True)),
                ('parent_section', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='source_material.contentsection')),
                ('document', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sections', to='source_material.sourcedocument')),
            ],
            options={
                'ordering': ['document', 'page_number'],
            },
        ),
        migrations.CreateModel(
            name='CharacterClass',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField()),
                ('primary_stats', models.CharField(max_length=200)),
                ('hit_points_base', models.IntegerField(default=10)),
                ('hit_points_per_level', models.IntegerField(default=5)),
                ('source_section', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='source_material.contentsection')),
            ],
        ),
        migrations.CreateModel(
            name='Ancestry',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField()),
                ('size', models.CharField(default='Medium', max_length=50)),
                ('speed', models.IntegerField(default=25)),
                ('special_abilities', models.TextField(blank=True)),
                ('source_section', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='source_material.contentsection')),
            ],
        ),
        migrations.CreateModel(
            name='Equipment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, unique=True)),
                ('description', models.TextField()),
                ('item_type', models.CharField(max_length=50)),
                ('cost', models.CharField(blank=True, max_length=100)),
                ('weight', models.CharField(blank=True, max_length=50)),
                ('damage_die', models.CharField(blank=True, max_length=20)),
                ('weapon_tags', models.CharField(blank=True, max_length=200)),
                ('armor_value', models.IntegerField(default=0)),
                ('source_section', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='source_material.contentsection')),
            ],
        ),
        migrations.CreateModel(
            name='Rule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('content', models.TextField()),
                ('category', models.CharField(max_length=100)),
                ('tags', models.CharField(blank=True, max_length=300)),
                ('source_section', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='source_material.contentsection')),
            ],
            options={
                'ordering': ['category', 'title'],
            },
        ),
        migrations.CreateModel(
            name='Spell',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, unique=True)),
                ('description', models.TextField()),
                ('level', models.IntegerField()),
                ('domain', models.CharField(max_length=100)),
                ('casting_time', models.CharField(max_length=100)),
                ('range_distance', models.CharField(max_length=100)),
                ('duration', models.CharField(max_length=100)),
                ('components', models.CharField(blank=True, max_length=200)),
                ('source_section', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='source_material.contentsection')),
            ],
            options={
                'ordering': ['level', 'name'],
            },
        ),
        migrations.CreateModel(
            name='Subclass',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField()),
                ('character_class', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='subclasses', to='source_material.characterclass')),
                ('source_section', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='source_material.contentsection')),
            ],
            options={
                'unique_together': {('name', 'character_class')},
            },
        ),
    ]
