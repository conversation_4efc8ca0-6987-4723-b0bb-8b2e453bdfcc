/* Daggerheart Master Custom Styles */

:root {
    --primary-color: #8B4513;
    --secondary-color: #D2691E;
    --accent-color: #CD853F;
    --dark-color: #2C1810;
    --light-color: #F5F5DC;
}

body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Navigation */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.navbar-dark .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.9);
    transition: color 0.3s ease;
}

.navbar-dark .navbar-nav .nav-link:hover {
    color: var(--secondary-color);
}

/* Cards */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: var(--primary-color);
    color: white;
    border-radius: 10px 10px 0 0 !important;
    border: none;
}

/* Buttons */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--dark-color);
    border-color: var(--dark-color);
}

.btn-secondary {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-secondary:hover {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}

/* Status Indicator */
.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
}

/* Dice Animation */
@keyframes diceRoll {
    0% { transform: rotate(0deg); }
    25% { transform: rotate(90deg); }
    50% { transform: rotate(180deg); }
    75% { transform: rotate(270deg); }
    100% { transform: rotate(360deg); }
}

.dice-rolling {
    animation: diceRoll 0.5s ease-in-out;
}

/* Character Sheet Styles */
.character-sheet {
    background: linear-gradient(135deg, #f5f5dc 0%, #e6e6d4 100%);
    border: 2px solid var(--primary-color);
    border-radius: 15px;
    padding: 20px;
    margin: 10px 0;
}

.stat-block {
    background-color: white;
    border: 1px solid var(--accent-color);
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    margin: 5px;
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    color: var(--primary-color);
}

.stat-modifier {
    font-size: 1.2rem;
    color: var(--secondary-color);
}

/* Campaign Cards */
.campaign-card {
    background: linear-gradient(135deg, #8B4513 0%, #D2691E 100%);
    color: white;
    border-radius: 15px;
    padding: 20px;
    margin: 10px 0;
}

.campaign-card .card-title {
    color: white;
    font-size: 1.5rem;
    margin-bottom: 10px;
}

/* Source Material */
.source-item {
    border-left: 4px solid var(--primary-color);
    padding-left: 15px;
    margin: 10px 0;
    background-color: white;
    border-radius: 0 8px 8px 0;
    padding: 15px;
}

.source-item h6 {
    color: var(--primary-color);
    margin-bottom: 5px;
}

/* Dice Roll Results */
.dice-result {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    margin: 10px 0;
    font-size: 1.2rem;
}

.dice-result .result-value {
    font-size: 3rem;
    font-weight: bold;
    margin: 10px 0;
}

.dice-result.critical-success {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    color: #333;
}

.dice-result.critical-failure {
    background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
}

/* Loading Spinner */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

/* Responsive Design */
@media (max-width: 768px) {
    .card {
        margin-bottom: 15px;
    }
    
    .stat-block {
        margin: 5px 0;
    }
    
    .character-sheet {
        padding: 15px;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #e0e0e0;
    }
    
    .card {
        background-color: #2d2d2d;
        color: #e0e0e0;
    }
    
    .stat-block {
        background-color: #3d3d3d;
        border-color: var(--accent-color);
    }
}

/* Utility Classes */
.text-primary-custom {
    color: var(--primary-color) !important;
}

.bg-primary-custom {
    background-color: var(--primary-color) !important;
}

.border-primary-custom {
    border-color: var(--primary-color) !important;
}

/* Footer */
footer {
    margin-top: auto;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--dark-color);
}
